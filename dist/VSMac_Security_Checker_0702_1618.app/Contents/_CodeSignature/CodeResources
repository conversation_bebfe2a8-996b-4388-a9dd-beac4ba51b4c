<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/_tcl_data/auto.tcl</key>
		<data>
		q3LD5+JJwylBqn63fZx2SxIFWog=
		</data>
		<key>Resources/_tcl_data/clock.tcl</key>
		<data>
		GI02QfnLLcRREBDxjNpDXSgroas=
		</data>
		<key>Resources/_tcl_data/encoding/ascii.enc</key>
		<data>
		3YMzPcHIOL65EC8GOXHMwgzE/YA=
		</data>
		<key>Resources/_tcl_data/encoding/big5.enc</key>
		<data>
		njXr89U4DjS5L+J0QST5MkuQHdM=
		</data>
		<key>Resources/_tcl_data/encoding/cns11643.enc</key>
		<data>
		QIEYQ6+V587xeVcQF9f6adFErXg=
		</data>
		<key>Resources/_tcl_data/encoding/cp1250.enc</key>
		<data>
		+iJxAw25AF1x+q1gtEdnlV1UMt0=
		</data>
		<key>Resources/_tcl_data/encoding/cp1251.enc</key>
		<data>
		YEOW2B/S2Q9XNP5sPyg/jxmqu2Q=
		</data>
		<key>Resources/_tcl_data/encoding/cp1252.enc</key>
		<data>
		LiEwDgvIqEfQQjZxsI08ZXYe4XI=
		</data>
		<key>Resources/_tcl_data/encoding/cp1253.enc</key>
		<data>
		j/mlJqVF0pOCmmeaLs3TOqb5qQ4=
		</data>
		<key>Resources/_tcl_data/encoding/cp1254.enc</key>
		<data>
		SEcFppWWydgT6jYWJcOkXGuzEig=
		</data>
		<key>Resources/_tcl_data/encoding/cp1255.enc</key>
		<data>
		Zg2+RYOSPL3/9iYbH630NJZYV5w=
		</data>
		<key>Resources/_tcl_data/encoding/cp1256.enc</key>
		<data>
		OP7jn0ThTDohmXj4tuTaVIFSz9Y=
		</data>
		<key>Resources/_tcl_data/encoding/cp1257.enc</key>
		<data>
		zBA8U7O6F2RxRYfq69ks0bx1GU0=
		</data>
		<key>Resources/_tcl_data/encoding/cp1258.enc</key>
		<data>
		YpTkLtItdWef8UZP9B1D2zsYJMI=
		</data>
		<key>Resources/_tcl_data/encoding/cp437.enc</key>
		<data>
		P3JSRcZgUNOdkjS6rOnQR6OEKUQ=
		</data>
		<key>Resources/_tcl_data/encoding/cp737.enc</key>
		<data>
		yV6k7T++8BPYEMC/sZOxX6it57g=
		</data>
		<key>Resources/_tcl_data/encoding/cp775.enc</key>
		<data>
		9DAaE0ChYOHygrX5i/n6y/qTsRk=
		</data>
		<key>Resources/_tcl_data/encoding/cp850.enc</key>
		<data>
		Xq03eI0STU7knsS4qhz2qqnChJw=
		</data>
		<key>Resources/_tcl_data/encoding/cp852.enc</key>
		<data>
		kEs1fDBgPfvPihCgVNk5lgixMd8=
		</data>
		<key>Resources/_tcl_data/encoding/cp855.enc</key>
		<data>
		+L1L9tlfZyy2G47KtYCnZb69rqU=
		</data>
		<key>Resources/_tcl_data/encoding/cp857.enc</key>
		<data>
		iISZ2d/fdcYMJ3A4akUA81dTznA=
		</data>
		<key>Resources/_tcl_data/encoding/cp860.enc</key>
		<data>
		xrHpMg7vRvyaI0N8JV5AheopgNs=
		</data>
		<key>Resources/_tcl_data/encoding/cp861.enc</key>
		<data>
		U1KXcupjIreUnbc+667ZHlpbo9o=
		</data>
		<key>Resources/_tcl_data/encoding/cp862.enc</key>
		<data>
		7yc2cdRoFfIplupjLSLMJ+uMpEs=
		</data>
		<key>Resources/_tcl_data/encoding/cp863.enc</key>
		<data>
		f27Ym9DUFcZNC4oDfwikf+rdFMQ=
		</data>
		<key>Resources/_tcl_data/encoding/cp864.enc</key>
		<data>
		4MpAC64PZu6+Tf4UfFoY3TsAt4w=
		</data>
		<key>Resources/_tcl_data/encoding/cp865.enc</key>
		<data>
		DrQP7rijglMLaXSOCL9RMSQjJAM=
		</data>
		<key>Resources/_tcl_data/encoding/cp866.enc</key>
		<data>
		QxJwk50+R5v5uaZj2eZ/zrp5QW8=
		</data>
		<key>Resources/_tcl_data/encoding/cp869.enc</key>
		<data>
		6BSfMzsYCdzN5Rz4tjMhA93n/DA=
		</data>
		<key>Resources/_tcl_data/encoding/cp874.enc</key>
		<data>
		2gV+H5P3VSGlHMcl1HEw9B5QnnA=
		</data>
		<key>Resources/_tcl_data/encoding/cp932.enc</key>
		<data>
		0LPesO5lOc5fKKUUZL+7OqA/KOU=
		</data>
		<key>Resources/_tcl_data/encoding/cp936.enc</key>
		<data>
		Ox0HsCrn47QHhIceF/NjMoNCaOY=
		</data>
		<key>Resources/_tcl_data/encoding/cp949.enc</key>
		<data>
		kEqLeEbTRSFjTIwJAT27HTGvR8o=
		</data>
		<key>Resources/_tcl_data/encoding/cp950.enc</key>
		<data>
		ZgX8yyNaCPkDK7RSMbGmMxdkZks=
		</data>
		<key>Resources/_tcl_data/encoding/dingbats.enc</key>
		<data>
		aiHVe0SghWq83mGxwWy5P05MPXQ=
		</data>
		<key>Resources/_tcl_data/encoding/ebcdic.enc</key>
		<data>
		RlUJxybEloCwI3JQGvelLwmrfVU=
		</data>
		<key>Resources/_tcl_data/encoding/euc-cn.enc</key>
		<data>
		vMyJkBW2iNXEJrx5HC/N46A6PrU=
		</data>
		<key>Resources/_tcl_data/encoding/euc-jp.enc</key>
		<data>
		+PyjmFAJos3Tl8s7rjCK8FsNfKw=
		</data>
		<key>Resources/_tcl_data/encoding/euc-kr.enc</key>
		<data>
		ic2k/mUVycA1UeThly/UeK86QZw=
		</data>
		<key>Resources/_tcl_data/encoding/gb12345.enc</key>
		<data>
		XI5pGuPBMwiCD0z2kgbXZc/VCUs=
		</data>
		<key>Resources/_tcl_data/encoding/gb1988.enc</key>
		<data>
		ScZjrCbB/k8P0UKMnvJwWK7mypU=
		</data>
		<key>Resources/_tcl_data/encoding/gb2312-raw.enc</key>
		<data>
		26CcZw8k1HuV0S1LuXBDkbgd2po=
		</data>
		<key>Resources/_tcl_data/encoding/gb2312.enc</key>
		<data>
		vMyJkBW2iNXEJrx5HC/N46A6PrU=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022-jp.enc</key>
		<data>
		OeILQc+osmk3evoG+cTWbt2Uass=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022-kr.enc</key>
		<data>
		lMXzklY2artozWfjAl8Xf1Ts050=
		</data>
		<key>Resources/_tcl_data/encoding/iso2022.enc</key>
		<data>
		nW8HdZilqG5utqTuwUgQv1JfvYk=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-1.enc</key>
		<data>
		pxtjgOo9I9wN4R07jOqGpMgGPUc=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-10.enc</key>
		<data>
		zseHxN54+du5e5xEBwzywSokaPc=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-11.enc</key>
		<data>
		AcVG7nwQsWSnTWY/pvwrx9MhIVU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-13.enc</key>
		<data>
		1GKTSgdO4T8sgQRj/QYQhJU/d7w=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-14.enc</key>
		<data>
		YsJTqnqGjOMliYaPqzczZUJFepY=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-15.enc</key>
		<data>
		SfekKIn7ig14yABnveGAlNvpVu4=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-16.enc</key>
		<data>
		UP2mxwoTPLZM84qksvMTtU0v2VU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-2.enc</key>
		<data>
		/2LrVxD94RB0qH2u6SKbz39m16A=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-3.enc</key>
		<data>
		sL6+3sU/+4lNn7DVfyWrKkWbbdU=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-4.enc</key>
		<data>
		zBwubDWwBcF+t7Gj10SYOoanVzY=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-5.enc</key>
		<data>
		+fZLtgFAaOLAc3GGxpS4EB3ZV14=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-6.enc</key>
		<data>
		uBBCY1TYV3GMyEHUJNoHDvufFE8=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-7.enc</key>
		<data>
		eswpSNXomXUMKsbIFM3hfUIRK8k=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-8.enc</key>
		<data>
		TqXsUzFUHt5lqc9gH1QY/Uts/Lw=
		</data>
		<key>Resources/_tcl_data/encoding/iso8859-9.enc</key>
		<data>
		9YXHClWJ3jlVjawBZ0P/heDF8DI=
		</data>
		<key>Resources/_tcl_data/encoding/jis0201.enc</key>
		<data>
		eutwjInBePtNVhHCReoafPZq3zo=
		</data>
		<key>Resources/_tcl_data/encoding/jis0208.enc</key>
		<data>
		qSKsrODBpKfdyS/l3XoRbTCjaGs=
		</data>
		<key>Resources/_tcl_data/encoding/jis0212.enc</key>
		<data>
		FQE0eHYEY6C841d7TWRuzbB2MrU=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-r.enc</key>
		<data>
		NmwTfALgabGpP7tdZLkSDqbprR8=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-ru.enc</key>
		<data>
		lccx/oU6osxBfUUWrvtLLwReU2M=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-t.enc</key>
		<data>
		vC1lEb+iw4MbVfpGqYkdE9raLmU=
		</data>
		<key>Resources/_tcl_data/encoding/koi8-u.enc</key>
		<data>
		qpbuTHWhrTV2lMjbwUaEgnhbgVs=
		</data>
		<key>Resources/_tcl_data/encoding/ksc5601.enc</key>
		<data>
		wv+kJ0V7STHlqSMm8lHNPWcQWbA=
		</data>
		<key>Resources/_tcl_data/encoding/macCentEuro.enc</key>
		<data>
		FrUdAXABaIoyy3sV3m56SfKLdv0=
		</data>
		<key>Resources/_tcl_data/encoding/macCroatian.enc</key>
		<data>
		Y/ROgYKEOE3gerDYsM1vfr/gmrk=
		</data>
		<key>Resources/_tcl_data/encoding/macCyrillic.enc</key>
		<data>
		PeF7KlhmJyYC+46cVJMKTNHzsGw=
		</data>
		<key>Resources/_tcl_data/encoding/macDingbats.enc</key>
		<data>
		pA5tuX1tsok6Bysiddwi4qTWBzc=
		</data>
		<key>Resources/_tcl_data/encoding/macGreek.enc</key>
		<data>
		nCrVPWn1B3hToF8JMzMLXW+IpRw=
		</data>
		<key>Resources/_tcl_data/encoding/macIceland.enc</key>
		<data>
		xDQlfXap/fgczNjMFCQsjjlA/Yk=
		</data>
		<key>Resources/_tcl_data/encoding/macJapan.enc</key>
		<data>
		uhh8UvrpeS2lv/vqp4H9TgcW4PY=
		</data>
		<key>Resources/_tcl_data/encoding/macRoman.enc</key>
		<data>
		5Njqag5wu3kzBMoh6xM3p6LCajE=
		</data>
		<key>Resources/_tcl_data/encoding/macRomania.enc</key>
		<data>
		4le9Fu80/cKdW2yYWhtFgBk3NUw=
		</data>
		<key>Resources/_tcl_data/encoding/macThai.enc</key>
		<data>
		Yz0ZC14oHPwBePbBHdchxqJm9kM=
		</data>
		<key>Resources/_tcl_data/encoding/macTurkish.enc</key>
		<data>
		OC40gkrYt57wyY/VFnUGSf2Usgo=
		</data>
		<key>Resources/_tcl_data/encoding/macUkraine.enc</key>
		<data>
		pIeUbLLv11/XSFA9deSVcgtT5bw=
		</data>
		<key>Resources/_tcl_data/encoding/shiftjis.enc</key>
		<data>
		LVaWWyQSXZmdECDHw0e4E6lyZHw=
		</data>
		<key>Resources/_tcl_data/encoding/symbol.enc</key>
		<data>
		8MAUttZ/wNwdG7xfBS8Mixxj2L8=
		</data>
		<key>Resources/_tcl_data/encoding/tis-620.enc</key>
		<data>
		RvsXtOCFAQIomg0D9PlWmzqkmU0=
		</data>
		<key>Resources/_tcl_data/history.tcl</key>
		<data>
		ZBritOdlyGpWPibSy382Xk3iffI=
		</data>
		<key>Resources/_tcl_data/http1.0/http.tcl</key>
		<data>
		vfJdcuRoG+zM5TTejFSRWY+hwQY=
		</data>
		<key>Resources/_tcl_data/http1.0/pkgIndex.tcl</key>
		<data>
		YAGligcB3/Il4lEKSq7mSJpTdlc=
		</data>
		<key>Resources/_tcl_data/init.tcl</key>
		<data>
		08ihDRHGSRyW81t0FEMvBCVl7fQ=
		</data>
		<key>Resources/_tcl_data/opt0.4/optparse.tcl</key>
		<data>
		mAKWQQWnNjvKScwDJu2+mRtW1SY=
		</data>
		<key>Resources/_tcl_data/opt0.4/pkgIndex.tcl</key>
		<data>
		BijHt83cyQHdeo+3uC/IUHDAcs8=
		</data>
		<key>Resources/_tcl_data/package.tcl</key>
		<data>
		xvz8AIOLCxb357dR8n3RFRbKUP8=
		</data>
		<key>Resources/_tcl_data/parray.tcl</key>
		<data>
		oLG9Tmjc4XaNPF4NPHsx4oAh07o=
		</data>
		<key>Resources/_tcl_data/safe.tcl</key>
		<data>
		DbqULAyylaDKpaN5gZuUTeT4SRc=
		</data>
		<key>Resources/_tcl_data/tclAppInit.c</key>
		<data>
		km85h6X//lSHequmKjsANnlOM48=
		</data>
		<key>Resources/_tcl_data/tclDTrace.d</key>
		<data>
		MMrg/n4rY5zjhot+mX06uuK1DAI=
		</data>
		<key>Resources/_tcl_data/tclIndex</key>
		<data>
		TJBi9Pynggw4xKNop0EugaDiuBg=
		</data>
		<key>Resources/_tcl_data/tm.tcl</key>
		<data>
		s1YLIYyMARZeSFvx/qCPB3iCchs=
		</data>
		<key>Resources/_tcl_data/word.tcl</key>
		<data>
		EHWAJskYyJheR9QvBglh+o7+vdk=
		</data>
		<key>Resources/_tk_data/bgerror.tcl</key>
		<data>
		ThycTe1bKIcdTJwWNWggUgaxihw=
		</data>
		<key>Resources/_tk_data/button.tcl</key>
		<data>
		PguQVCjCk/IQdBRf5DKB8i5pnrQ=
		</data>
		<key>Resources/_tk_data/choosedir.tcl</key>
		<data>
		b4+6WX8IUIPK3Y5xk9F2/qCinOc=
		</data>
		<key>Resources/_tk_data/clrpick.tcl</key>
		<data>
		TAQKzEzSEXP49phjV0NARrvQzDc=
		</data>
		<key>Resources/_tk_data/comdlg.tcl</key>
		<data>
		UeK1uukZTnsbvTTK/Ko9IrXhxrc=
		</data>
		<key>Resources/_tk_data/console.tcl</key>
		<data>
		FhAPdcjHT8PgEH9Jim/hl7gUi3k=
		</data>
		<key>Resources/_tk_data/dialog.tcl</key>
		<data>
		wYBuvfc08XAqPlQl+5FaAImE8Hw=
		</data>
		<key>Resources/_tk_data/entry.tcl</key>
		<data>
		UboQkLAvC5oVA8URpH0RFzlOE8E=
		</data>
		<key>Resources/_tk_data/focus.tcl</key>
		<data>
		aDWlFehanlXVonBz2uHxpddCRRM=
		</data>
		<key>Resources/_tk_data/fontchooser.tcl</key>
		<data>
		e6wFZyls0Z4ft+nRs67in1XccN4=
		</data>
		<key>Resources/_tk_data/iconlist.tcl</key>
		<data>
		o675ntOsggS/0Q32SWt/Lp3Lsuk=
		</data>
		<key>Resources/_tk_data/icons.tcl</key>
		<data>
		Jq4ArEnwnaDs/x2y8CGYOCO3fxI=
		</data>
		<key>Resources/_tk_data/images/README</key>
		<data>
		8boyJEjSBmI/j+c0GS84PY9/oZg=
		</data>
		<key>Resources/_tk_data/images/logo.eps</key>
		<data>
		K0mbfE68hVTswHuECGMsr0B/ttU=
		</data>
		<key>Resources/_tk_data/images/logo100.gif</key>
		<data>
		vLc9ivJihGOhuVVYGZnHfwn4Bbg=
		</data>
		<key>Resources/_tk_data/images/logo64.gif</key>
		<data>
		6lIhmjehQP2YrqZupUaF3YFY2bE=
		</data>
		<key>Resources/_tk_data/images/logoLarge.gif</key>
		<data>
		3dEOeYryCe/OAi6XRI5e4RzrViE=
		</data>
		<key>Resources/_tk_data/images/logoMed.gif</key>
		<data>
		E0iOTyhnbx4M44P4DRNRDwcZi5k=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo.eps</key>
		<data>
		ilj8GbIL/ciRNRXZsyzL+Kz5I0Q=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo100.gif</key>
		<data>
		vZceca6AXCwuUd1UTQBukjY7bAw=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo150.gif</key>
		<data>
		GhWCZQ4hiwvm/97/1k0n9Lmphw8=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo175.gif</key>
		<data>
		jbf7RTt5uPK05nrDCkultb3evTs=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo200.gif</key>
		<data>
		+pig/YkQ3y77FO2uwDi045H+qzw=
		</data>
		<key>Resources/_tk_data/images/pwrdLogo75.gif</key>
		<data>
		5bHe1JCVMyI2Q5U47NndCx/Uk0s=
		</data>
		<key>Resources/_tk_data/images/tai-ku.gif</key>
		<data>
		JnqVIMQ5AiHc5QF354mk69WQ9IQ=
		</data>
		<key>Resources/_tk_data/listbox.tcl</key>
		<data>
		qiZADGyG6c17jazOTKuAt64hqXg=
		</data>
		<key>Resources/_tk_data/megawidget.tcl</key>
		<data>
		n4k0mIglWV9zEc/w0OGn+45GNKQ=
		</data>
		<key>Resources/_tk_data/menu.tcl</key>
		<data>
		Ltd65Ojk6Jd8RhBBQDplHK9/c3A=
		</data>
		<key>Resources/_tk_data/mkpsenc.tcl</key>
		<data>
		kl2XO3AlI4TR3ps4jGwgOOZG/d8=
		</data>
		<key>Resources/_tk_data/msgbox.tcl</key>
		<data>
		RsGIL5eKTXpu0NLyIO3L2J27+z8=
		</data>
		<key>Resources/_tk_data/msgs/cs.msg</key>
		<data>
		lebHHkUlqN2R5Ii5UmZa6cX73e0=
		</data>
		<key>Resources/_tk_data/msgs/da.msg</key>
		<data>
		UprgsMudHbx/iETzRhSeFR3go2s=
		</data>
		<key>Resources/_tk_data/msgs/de.msg</key>
		<data>
		y0VfkQII4uVbJ6lqvYRf7tqIcRo=
		</data>
		<key>Resources/_tk_data/msgs/el.msg</key>
		<data>
		JVMd9iYuOxFwBVc1xah0uRJP6oM=
		</data>
		<key>Resources/_tk_data/msgs/en.msg</key>
		<data>
		V1QprquvZkBCWsG8OXszgsHtESI=
		</data>
		<key>Resources/_tk_data/msgs/en_gb.msg</key>
		<data>
		bC1rYiQpq4wX4HwuD1RkaYI6vlc=
		</data>
		<key>Resources/_tk_data/msgs/eo.msg</key>
		<data>
		pY/pWpYS3lzav/XdzuABmitlFgE=
		</data>
		<key>Resources/_tk_data/msgs/es.msg</key>
		<data>
		jDUW95+3LzKEi0AJHaZ8geQP3v4=
		</data>
		<key>Resources/_tk_data/msgs/fi.msg</key>
		<data>
		fepoJVjo3Oc47h97QpfMX5e180I=
		</data>
		<key>Resources/_tk_data/msgs/fr.msg</key>
		<data>
		ytOAWQDoYLlJHj7lwsD1KtymcGU=
		</data>
		<key>Resources/_tk_data/msgs/hu.msg</key>
		<data>
		GhSNIwyfjXSNlqec1OJhryZNZSQ=
		</data>
		<key>Resources/_tk_data/msgs/it.msg</key>
		<data>
		X7FjvBCG0zZiKCBAePIZ/ku2fLM=
		</data>
		<key>Resources/_tk_data/msgs/nl.msg</key>
		<data>
		j2r/aLQrdH0whw1tp+BYKUkhQGo=
		</data>
		<key>Resources/_tk_data/msgs/pl.msg</key>
		<data>
		PkXAECsoeQjXcKMdGQZnjnhQiMI=
		</data>
		<key>Resources/_tk_data/msgs/pt.msg</key>
		<data>
		lI7pX0VJ2ox9QSkR0XtLYsuiKt0=
		</data>
		<key>Resources/_tk_data/msgs/ru.msg</key>
		<data>
		FpDFn6nzZwDY86ptWYUAjuEls/Q=
		</data>
		<key>Resources/_tk_data/msgs/sv.msg</key>
		<data>
		KNnbnL7nkcCb0nLZwqbD2oDrieo=
		</data>
		<key>Resources/_tk_data/msgs/zh_cn.msg</key>
		<data>
		CRYOLe02340zZ4hksZ+HFZjuKSA=
		</data>
		<key>Resources/_tk_data/obsolete.tcl</key>
		<data>
		MOJFld1oPkcP6fEoFNJ9bSZrUR4=
		</data>
		<key>Resources/_tk_data/optMenu.tcl</key>
		<data>
		l0XIPuyFZWAvjXRhBCSEgAn/pnA=
		</data>
		<key>Resources/_tk_data/palette.tcl</key>
		<data>
		oyLM+zP/c+SkcwtbId5CkPnZRiI=
		</data>
		<key>Resources/_tk_data/panedwindow.tcl</key>
		<data>
		3+PcZjwZ6aUFJqUTBD0jk4adj5A=
		</data>
		<key>Resources/_tk_data/pkgIndex.tcl</key>
		<data>
		XOp1V7FWeHT9who12RwpezekOys=
		</data>
		<key>Resources/_tk_data/safetk.tcl</key>
		<data>
		0ZOw5gErQuu06VsONbGpzawlIhw=
		</data>
		<key>Resources/_tk_data/scale.tcl</key>
		<data>
		PlppetqYRK09qYBe2RIx0nCM88E=
		</data>
		<key>Resources/_tk_data/scrlbar.tcl</key>
		<data>
		I+b3CVBm7TtlmYMkAh1mXYEOapM=
		</data>
		<key>Resources/_tk_data/spinbox.tcl</key>
		<data>
		mxZmqeyYka/MzK+9XM/L/pBOhdQ=
		</data>
		<key>Resources/_tk_data/tclIndex</key>
		<data>
		/CEDJvFTjko57lytP8tP81KHJCk=
		</data>
		<key>Resources/_tk_data/tearoff.tcl</key>
		<data>
		qKEoPyejUVzAzyijvYfWV79Vnh8=
		</data>
		<key>Resources/_tk_data/text.tcl</key>
		<data>
		TLr1vX72+ATQqphoF0bhQ/ytO2I=
		</data>
		<key>Resources/_tk_data/tk.tcl</key>
		<data>
		umpL3Zq1rruoo8r+3eHBvRKb1Ko=
		</data>
		<key>Resources/_tk_data/tkAppInit.c</key>
		<data>
		8JMX4S2O4t0KVuZHQ2za78mjZAQ=
		</data>
		<key>Resources/_tk_data/tkfbox.tcl</key>
		<data>
		LQrPWwmF+yYZY9yq4DNHdjQ44xE=
		</data>
		<key>Resources/_tk_data/ttk/altTheme.tcl</key>
		<data>
		1GsRcUh7gTBmjeeUo8x+dqDlLj4=
		</data>
		<key>Resources/_tk_data/ttk/aquaTheme.tcl</key>
		<data>
		sotleIKW5LSsMJKoV94sVjC0GAo=
		</data>
		<key>Resources/_tk_data/ttk/button.tcl</key>
		<data>
		AXomewLff3oqIW8UuGgM/wQhQBE=
		</data>
		<key>Resources/_tk_data/ttk/clamTheme.tcl</key>
		<data>
		Qg0b3exeM+jh8zfg7vI2Yct6g1o=
		</data>
		<key>Resources/_tk_data/ttk/classicTheme.tcl</key>
		<data>
		1emFlDqkPq+ljpDSvsdIW2doe9Y=
		</data>
		<key>Resources/_tk_data/ttk/combobox.tcl</key>
		<data>
		AdLHr5j79DIwP828h6T2cZ0Pc7A=
		</data>
		<key>Resources/_tk_data/ttk/cursors.tcl</key>
		<data>
		DddlHcbhwfX1FmoNlY7+BUW+gJ0=
		</data>
		<key>Resources/_tk_data/ttk/defaults.tcl</key>
		<data>
		UDCWOoBCTzoHaSFAsdAoGqK/y50=
		</data>
		<key>Resources/_tk_data/ttk/entry.tcl</key>
		<data>
		mV33YkSBrPKdFCtlrr5NUjIuYl0=
		</data>
		<key>Resources/_tk_data/ttk/fonts.tcl</key>
		<data>
		Snv5G+hfEN/He3uOA7N+NO5R7q8=
		</data>
		<key>Resources/_tk_data/ttk/menubutton.tcl</key>
		<data>
		lcLabSCzCUXS2BZhbPyTH0iOn/c=
		</data>
		<key>Resources/_tk_data/ttk/notebook.tcl</key>
		<data>
		rwHBo5Y9koMB1rwslNDYfd3uA0Q=
		</data>
		<key>Resources/_tk_data/ttk/panedwindow.tcl</key>
		<data>
		rD6tifSHR/5zAcKGVBDQ1cj/YA0=
		</data>
		<key>Resources/_tk_data/ttk/progress.tcl</key>
		<data>
		0HCgHMWnhySbxtrRhLJJxN03OWo=
		</data>
		<key>Resources/_tk_data/ttk/scale.tcl</key>
		<data>
		g2QnCoeAp1uLLC1RceoH+ianNls=
		</data>
		<key>Resources/_tk_data/ttk/scrollbar.tcl</key>
		<data>
		N41GZpiT3gQWS4o/QB/O628B84g=
		</data>
		<key>Resources/_tk_data/ttk/sizegrip.tcl</key>
		<data>
		pFjqXy3bu8ySGz0+YAzkIGUmTmE=
		</data>
		<key>Resources/_tk_data/ttk/spinbox.tcl</key>
		<data>
		9qZnarr+F4GNITEpohU2bzkLgQs=
		</data>
		<key>Resources/_tk_data/ttk/treeview.tcl</key>
		<data>
		I9LaoEaT61JA5Ax0PL/wYUt8Bxk=
		</data>
		<key>Resources/_tk_data/ttk/ttk.tcl</key>
		<data>
		PdNEvbMBQO29Z6AwhgDzXN2Tht0=
		</data>
		<key>Resources/_tk_data/ttk/utils.tcl</key>
		<data>
		UsD05zN5bJ2bs2qKjFqaXoxHzm8=
		</data>
		<key>Resources/_tk_data/ttk/vistaTheme.tcl</key>
		<data>
		xD5OERTTumziooC+SGXiSsnNyks=
		</data>
		<key>Resources/_tk_data/ttk/winTheme.tcl</key>
		<data>
		TZUB1UV/62RgzeyMwOjfH05WsTU=
		</data>
		<key>Resources/_tk_data/ttk/xpTheme.tcl</key>
		<data>
		IMDfjiaT+VHMgaH5AnROAEzboCM=
		</data>
		<key>Resources/_tk_data/unsupported.tcl</key>
		<data>
		gIL95QxCjSURsF9Sn8zwJlHVrJM=
		</data>
		<key>Resources/_tk_data/xmfbox.tcl</key>
		<data>
		GBqdjL2eeFxpGUmsJTamHaS0USo=
		</data>
		<key>Resources/base_library.zip</key>
		<data>
		u4U5kCHp0vomXwOjG4QuhIPtU/U=
		</data>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<data>
		INBhs7dCz6MeX7yGLTT1V1NO/b8=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<data>
		/dyLHGiO87rtDVpGq/XwHw7a8Cs=
		</data>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<data>
		hNECSIc4sOu8elCHlz7/vVTJW9U=
		</data>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<data>
		s2ExZOWH0JwFLDTM3E1E2sT/ROI=
		</data>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<data>
		Bv/IEe5RYJgJ2IiUAi4iKzOa7+4=
		</data>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<data>
		+evgfnnhRvedyIp/+JQsDkMEnw0=
		</data>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<data>
		TwB32rbJhqZKuTkmMAJMsJdyseg=
		</data>
		<key>Resources/img/123.webp</key>
		<data>
		tU9ln24m/WldMzrodXCCV+zrzeE=
		</data>
		<key>Resources/img/bluetooth.icns</key>
		<data>
		BWQueLjvWPlbIq/Kh/iUbQpap6s=
		</data>
		<key>Resources/img/device.icns</key>
		<data>
		sV33xFn17BTEvfFLUBfa2Yv1sxY=
		</data>
		<key>Resources/img/erase.png</key>
		<data>
		9BPaprJeiwNOEmv1HSsD2/Yi7mg=
		</data>
		<key>Resources/img/exit.png</key>
		<data>
		VWk+nE0rFweKNEjgXsZAgVd9rHI=
		</data>
		<key>Resources/img/findmy.icns</key>
		<data>
		EzmzWjuyERvTr2fQDimaOfU3Iqw=
		</data>
		<key>Resources/img/shutdown.png</key>
		<data>
		K0UjnvC4DqC907tryPgh+wfMy/0=
		</data>
		<key>Resources/img/sysinfo.icns</key>
		<data>
		Ltqqxr6FZCqK7Arl2g1zyZe3mCQ=
		</data>
		<key>Resources/img/vs_icns/vs_mac_tool_v2.png</key>
		<data>
		tm8iNmkmZ8VXl/C4cczT2Mh3RD0=
		</data>
		<key>Resources/img/vs_icns/vsmactool.jpeg</key>
		<data>
		ldkboZcNMcgtxMqVzFijk+UAV4s=
		</data>
		<key>Resources/img/vs_icns/vsmactool2.icns</key>
		<data>
		6DLidyRNkn4oTIyC6LI0fS/sDMM=
		</data>
		<key>Resources/img/vsmactool.icns</key>
		<data>
		/bZf6q4HmLDhO0YEfkZNtGl9Frw=
		</data>
		<key>Resources/img/vsmactool.png</key>
		<data>
		nWqWQE/6RLXgjcE53X1jr7QTYzg=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/LICENSE</key>
		<data>
		BEXtD2mRDuruA28Jo5oTxuHzfhI=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/METADATA</key>
		<data>
		WRVfQD62iIV68exEpB8/CT8tAzw=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/RECORD</key>
		<data>
		z0uI1lqVRk34e2pzOxAYtYMRuCc=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/WHEEL</key>
		<data>
		8dH5Vh360Fue0WnPyApHzleku2o=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/entry_points.txt</key>
		<data>
		iRVBKy2hRNmOZD53OXV7DrKY+sc=
		</data>
		<key>Resources/keyring-25.6.0.dist-info/top_level.txt</key>
		<data>
		+Bx3AhWqVcspD92h48yte3axJNs=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER</key>
		<data>
		16AxQdXWseiLa1nvCLZoHfISxZk=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE</key>
		<data>
		K4uBUimqimHkg/tLoFiLi2xJGJA=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA</key>
		<data>
		6Mgw2LCUIwDHyHs7j9FeoTluB70=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD</key>
		<data>
		s5THrsFYNQuvZ2rjGXvvTXFYsxw=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED</key>
		<data>
		2jmj7l5rSw0yVb/vlWAYkK/YBwk=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL</key>
		<data>
		eCm0Mku1QnmUlBMaJw7Dva1N7e8=
		</data>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt</key>
		<data>
		TmH5Jk3nR4O1kkJJvP4bBvF4ua0=
		</data>
		<key>Resources/setuptools/_vendor/jaraco/text/Lorem ipsum.txt</key>
		<data>
		eDY2ciZNnNP3LVwdNmXhZXsaUHE=
		</data>
		<key>Resources/tcl8/8.4/platform-1.0.19.tm</key>
		<data>
		YdK+5LeSYCeDUJi5lbT1zNrO1NU=
		</data>
		<key>Resources/tcl8/8.4/platform/shell-1.1.4.tm</key>
		<data>
		U3+wEz8cvVA4oE+m5N6x4Yi+Ih4=
		</data>
		<key>Resources/tcl8/8.5/msgcat-1.6.1.tm</key>
		<data>
		fCszIWqED2pr973Gzjo+PX6em64=
		</data>
		<key>Resources/tcl8/8.5/tcltest-2.5.9.tm</key>
		<data>
		rXyZWk5SH90mbh4m01YIZsAKPXc=
		</data>
		<key>Resources/tcl8/8.6/http-2.9.8.tm</key>
		<data>
		BGYeT0dfxJkObssb5uPqYMxlXYo=
		</data>
		<key>Resources/vsmactool.icns</key>
		<data>
		/bZf6q4HmLDhO0YEfkZNtGl9Frw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/PIL/.dylibs</key>
		<dict>
			<key>symlink</key>
			<string>__dot__dylibs</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libXau.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			oquNTqBnHpMlz5394zrC17FGi80=
			</data>
			<key>requirement</key>
			<string>cdhash H"a2ab8d4ea0671e9325cf9dfde33ac2d7b1468bcd"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			x7R3O7pZUNBEfQAdsl54ix4LaJ4=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7b4773bba5950d0447d001db25e788b1e0b689e"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblcms2.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			vOar1FXZ7WcuWRqbNA5cYJVqmWw=
			</data>
			<key>requirement</key>
			<string>cdhash H"bce6abd455d9ed672e591a9b340e5c60956a996c"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/liblzma.5.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			IEdMZTK9mMXiCDCIQU45IH9mD9E=
			</data>
			<key>requirement</key>
			<string>cdhash H"20474c6532bd98c5e2083088414e39207f660fd1"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			8aTGLWPNL8Z0mq/ZCCqo4ITzkTw=
			</data>
			<key>requirement</key>
			<string>cdhash H"f1a4c62d63cd2fc6749aafd9082aa8e084f3913c"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libsharpyuv.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			a4upi7rj26KgmwWmOVWseAwGv+8=
			</data>
			<key>requirement</key>
			<string>cdhash H"6b8ba98bbae3dba2a09b05a63955ac780c06bfef"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libtiff.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			x9bP0OkLtFZ19oat7KODBojVte8=
			</data>
			<key>requirement</key>
			<string>cdhash H"c7d6cfd0e90bb45675f686adeca3830688d5b5ef"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebp.7.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			2JRI3oDk/hiPO6KHLr1W3RGSs9g=
			</data>
			<key>requirement</key>
			<string>cdhash H"d89448de80e4fe188f3ba2872ebd56dd1192b3d8"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpdemux.2.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			S9OWwquTmkEKpIFW/jqzt1HHtug=
			</data>
			<key>requirement</key>
			<string>cdhash H"4bd396c2ab939a410aa48156fe3ab3b751c7b6e8"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libwebpmux.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			jeRVXzKhN1QeR2XcCm4BMJPTLUE=
			</data>
			<key>requirement</key>
			<string>cdhash H"8de4555f32a137541e4765dc0a6e013093d32d41"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libxcb.1.1.0.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			oFtR66WJm5ejzYocVozZR9+7P+A=
			</data>
			<key>requirement</key>
			<string>cdhash H"a05b51eba5899b97a3cd8a1c568cd947dfbb3fe0"</string>
		</dict>
		<key>Frameworks/PIL/__dot__dylibs/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			CA17m/ktNfM9WuMFAkoquWHpDmA=
			</data>
			<key>requirement</key>
			<string>cdhash H"080d7b9bf92d35f33d5ae305024a2ab961e90e60"</string>
		</dict>
		<key>Frameworks/PIL/_imaging.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			fftQYD2+DMiBrRr160bY3mhKBgo=
			</data>
			<key>requirement</key>
			<string>cdhash H"7dfb50603dbe0cc881ad1af5eb46d8de684a060a"</string>
		</dict>
		<key>Frameworks/PIL/_imagingcms.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			+hFMLJChFuwmZithzd9kIWZthwE=
			</data>
			<key>requirement</key>
			<string>cdhash H"fa114c2c90a116ec26662b61cddf6421666d8701"</string>
		</dict>
		<key>Frameworks/PIL/_imagingmath.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			H8bKCo8qCn9xFZHoP8rFEEz+ucw=
			</data>
			<key>requirement</key>
			<string>cdhash H"1fc6ca0a8f2a0a7f711591e83fcac5104cfeb9cc"</string>
		</dict>
		<key>Frameworks/PIL/_imagingtk.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			e4fne8x04eMcg+1EIFPRoe0u9lU=
			</data>
			<key>requirement</key>
			<string>cdhash H"7b87e77bcc74e1e31c83ed442053d1a1ed2ef655"</string>
		</dict>
		<key>Frameworks/PIL/_webp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			6B66VAUB4KMTpLFZNwLGeb+VU3I=
			</data>
			<key>requirement</key>
			<string>cdhash H"e81eba540501e0a313a4b1593702c679bf955372"</string>
		</dict>
		<key>Frameworks/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.13/Python</string>
		</dict>
		<key>Frameworks/Python.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			Rb2PC6A2foNd7sN7tJvFTt8znlI=
			</data>
			<key>requirement</key>
			<string>cdhash H"45bd8f0ba0367e835deec37bb49bc54edf339e52"</string>
		</dict>
		<key>Frameworks/Tcl</key>
		<dict>
			<key>cdhash</key>
			<data>
			Qf5NK4TndwvSqvX6zHZTfiyHmXQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"41fe4d2b84e7770bd2aaf5facc76537e2c879974"</string>
		</dict>
		<key>Frameworks/Tk</key>
		<dict>
			<key>cdhash</key>
			<data>
			Pb8uEEVmMAGEICGUk5LfvzSBo8s=
			</data>
			<key>requirement</key>
			<string>cdhash H"3dbf2e1045663001842021949392dfbf3481a3cb"</string>
		</dict>
		<key>Frameworks/_tcl_data</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/_tcl_data</string>
		</dict>
		<key>Frameworks/_tk_data</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/_tk_data</string>
		</dict>
		<key>Frameworks/base_library.zip</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/base_library.zip</string>
		</dict>
		<key>Frameworks/customtkinter</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/customtkinter</string>
		</dict>
		<key>Frameworks/img</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/img</string>
		</dict>
		<key>Frameworks/keyring-25.6.0.dist-info</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/keyring-25.6.0.dist-info</string>
		</dict>
		<key>Frameworks/lib-dynload/_asyncio.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			20aUys4UljegA11OPiSCBtWv1I4=
			</data>
			<key>requirement</key>
			<string>cdhash H"db4694cace149637a0035d4e3e248206d5afd48e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bisect.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			fTTdKgXKAF82o09KJzhhFFgsEmo=
			</data>
			<key>requirement</key>
			<string>cdhash H"7d34dd2a05ca005f36a34f4a27386114582c126a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_blake2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			z8NJMs+ZWzQA25MZ8tDTdwQ9MXw=
			</data>
			<key>requirement</key>
			<string>cdhash H"cfc34932cf995b3400db9319f2d0d377043d317c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_bz2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			11tnyYQEQ3RSWBDREflyhcr5xx4=
			</data>
			<key>requirement</key>
			<string>cdhash H"d75b67c984044374525810d111f97285caf9c71e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_cn.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Y1YZY5s58yHwjBID5JocDbVT0fk=
			</data>
			<key>requirement</key>
			<string>cdhash H"635619639b39f321f08c1203e49a1c0db553d1f9"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_hk.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			duXpQ639f5tmW89fVQuiNS95YpQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"76e5e943adfd7f9b665bcf5f550ba2352f796294"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_iso2022.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			TxHX2Us00g4fyvvQg2I0zNmxqWk=
			</data>
			<key>requirement</key>
			<string>cdhash H"4f11d7d94b34d20e1fcafbd0836234ccd9b1a969"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_jp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			GPwINHf6JbqpQSaL2onZciNNuTg=
			</data>
			<key>requirement</key>
			<string>cdhash H"18fc083477fa25baa941268bda89d972234db938"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_kr.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			9UnZ6h1H0SlA/HIjgIrTJ1Um0XU=
			</data>
			<key>requirement</key>
			<string>cdhash H"f549d9ea1d47d12940fc7223808ad3275526d175"</string>
		</dict>
		<key>Frameworks/lib-dynload/_codecs_tw.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			RxbQNpeSpaz7Y+clbGS7QryoBS4=
			</data>
			<key>requirement</key>
			<string>cdhash H"4716d0369792a5acfb63e7256c64bb42bca8052e"</string>
		</dict>
		<key>Frameworks/lib-dynload/_contextvars.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			mb81DLbA9A1zd3b2YUm3+Vo73Jo=
			</data>
			<key>requirement</key>
			<string>cdhash H"99bf350cb6c0f40d737776f66149b7f95a3bdc9a"</string>
		</dict>
		<key>Frameworks/lib-dynload/_csv.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			5eE8wfFy5jbs567OJl03/4x6sv8=
			</data>
			<key>requirement</key>
			<string>cdhash H"e5e13cc1f172e636ece7aece265d37ff8c7ab2ff"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ctypes.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			jmO4/Zo2bkOTlv1D37wsesvlTII=
			</data>
			<key>requirement</key>
			<string>cdhash H"8e63b8fd9a366e439396fd43dfbc2c7acbe54c82"</string>
		</dict>
		<key>Frameworks/lib-dynload/_curses.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			F3VcJZ0Rw8IY2jrmsKiTrG9/Z7U=
			</data>
			<key>requirement</key>
			<string>cdhash H"17755c259d11c3c218da3ae6b0a893ac6f7f67b5"</string>
		</dict>
		<key>Frameworks/lib-dynload/_datetime.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			GZEuP5JFNbHtFTyntXCIUEc8Pwc=
			</data>
			<key>requirement</key>
			<string>cdhash H"19912e3f924535b1ed153ca7b5708850473c3f07"</string>
		</dict>
		<key>Frameworks/lib-dynload/_decimal.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			S3tC+VVCRWGlkotl77obYf6tl4s=
			</data>
			<key>requirement</key>
			<string>cdhash H"4b7b42f955424561a5928b65efba1b61fead978b"</string>
		</dict>
		<key>Frameworks/lib-dynload/_elementtree.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hwWCxJTwZas2XaTQB37cXN/lcTw=
			</data>
			<key>requirement</key>
			<string>cdhash H"870582c494f065ab365da4d0077edc5cdfe5713c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_hashlib.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			EbBO7Z8+5bHWtfOPVFvr5XxGekc=
			</data>
			<key>requirement</key>
			<string>cdhash H"11b04eed9f3ee5b1d6b5f38f545bebe57c467a47"</string>
		</dict>
		<key>Frameworks/lib-dynload/_heapq.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			4EvQnGCeHO46v8jNSLy9HNysFtE=
			</data>
			<key>requirement</key>
			<string>cdhash H"e04bd09c609e1cee3abfc8cd48bcbd1cdcac16d1"</string>
		</dict>
		<key>Frameworks/lib-dynload/_json.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			VOomZbA95MiwUxQD7WjpdqR2dRc=
			</data>
			<key>requirement</key>
			<string>cdhash H"54ea2665b03de4c8b0531403ed68e976a4767517"</string>
		</dict>
		<key>Frameworks/lib-dynload/_lzma.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			yTVjTyp/x97PDETBwP2ZzmubCGk=
			</data>
			<key>requirement</key>
			<string>cdhash H"c935634f2a7fc7decf0c44c1c0fd99ce6b9b0869"</string>
		</dict>
		<key>Frameworks/lib-dynload/_md5.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			B0KL3kOa+4N9TdTmX7nBjgzHYGE=
			</data>
			<key>requirement</key>
			<string>cdhash H"07428bde439afb837d4dd4e65fb9c18e0cc76061"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multibytecodec.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Z1g0+UkdY9m/24k/9DaBrtSQ3Mw=
			</data>
			<key>requirement</key>
			<string>cdhash H"675834f9491d63d9bfdb893ff43681aed490dccc"</string>
		</dict>
		<key>Frameworks/lib-dynload/_multiprocessing.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			DBB0kW8RHnblr9DjPLSbpkJFfJw=
			</data>
			<key>requirement</key>
			<string>cdhash H"0c1074916f111e76e5afd0e33cb49ba642457c9c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_opcode.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hQ75jrrKknnG1kgqk/dXLdMOt9M=
			</data>
			<key>requirement</key>
			<string>cdhash H"850ef98ebaca9279c6d6482a93f7572dd30eb7d3"</string>
		</dict>
		<key>Frameworks/lib-dynload/_pickle.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			CuYVAO297SbaUKFArZxBf44b7ZU=
			</data>
			<key>requirement</key>
			<string>cdhash H"0ae61500edbded26da50a140ad9c417f8e1bed95"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixshmem.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			c4/WV9BCeXugkCesc09SN6CpT1I=
			</data>
			<key>requirement</key>
			<string>cdhash H"738fd657d042797ba09027ac734f5237a0a94f52"</string>
		</dict>
		<key>Frameworks/lib-dynload/_posixsubprocess.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			dEDb9bud6GpEdFPbS99vFhUEu9U=
			</data>
			<key>requirement</key>
			<string>cdhash H"7440dbf5bb9de86a447453db4bdf6f161504bbd5"</string>
		</dict>
		<key>Frameworks/lib-dynload/_queue.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			6OOfI5ZiJDqDOp+aksg6AmJM6vg=
			</data>
			<key>requirement</key>
			<string>cdhash H"e8e39f239662243a833a9f9a92c83a02624ceaf8"</string>
		</dict>
		<key>Frameworks/lib-dynload/_random.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			8LGIHR+04V5g+xuu/hW15+SiqeQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"f0b1881d1fb4e15e60fb1baefe15b5e7e4a2a9e4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_scproxy.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			IABC67gRZ2fJW1ank3fXXnC5E2c=
			</data>
			<key>requirement</key>
			<string>cdhash H"200042ebb8116767c95b56a79377d75e70b91367"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha1.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			gJfbEICCrhU9gTYsjqPnH9owscg=
			</data>
			<key>requirement</key>
			<string>cdhash H"8097db108082ae153d81362c8ea3e71fda30b1c8"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha2.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			jxWHmVX4MNcA5Z0XGAyfm8uZtWw=
			</data>
			<key>requirement</key>
			<string>cdhash H"8f15879955f830d700e59d17180c9f9bcb99b56c"</string>
		</dict>
		<key>Frameworks/lib-dynload/_sha3.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			0AOKWgIYIbeAB1xSdB4QBM54ZOw=
			</data>
			<key>requirement</key>
			<string>cdhash H"d0038a5a021821b780075c52741e1004ce7864ec"</string>
		</dict>
		<key>Frameworks/lib-dynload/_socket.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			QFhhCtrOX3LrwxTnY4vgxEL9AmA=
			</data>
			<key>requirement</key>
			<string>cdhash H"4058610adace5f72ebc314e7638be0c442fd0260"</string>
		</dict>
		<key>Frameworks/lib-dynload/_ssl.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			u2Q5EgQr8XOqlipXCQ+Ai3z2v9Q=
			</data>
			<key>requirement</key>
			<string>cdhash H"bb643912042bf173aa962a57090f808b7cf6bfd4"</string>
		</dict>
		<key>Frameworks/lib-dynload/_statistics.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			IRvpqwp1AoQhjW7g3X/sHNvHrtg=
			</data>
			<key>requirement</key>
			<string>cdhash H"211be9ab0a750284218d6ee0dd7fec1cdbc7aed8"</string>
		</dict>
		<key>Frameworks/lib-dynload/_struct.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			qGuk8zKcjj8iyDxRNIWOEd0Uht8=
			</data>
			<key>requirement</key>
			<string>cdhash H"a86ba4f3329c8e3f22c83c5134858e11dd1486df"</string>
		</dict>
		<key>Frameworks/lib-dynload/_tkinter.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			c2Ky7b8SugGZDonUhb7S3kLYvtU=
			</data>
			<key>requirement</key>
			<string>cdhash H"7362b2edbf12ba01990e89d485bed2de42d8bed5"</string>
		</dict>
		<key>Frameworks/lib-dynload/array.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			pgL/EnMRlcrfOh2fri5dTonyxss=
			</data>
			<key>requirement</key>
			<string>cdhash H"a602ff12731195cadf3a1d9fae2e5d4e89f2c6cb"</string>
		</dict>
		<key>Frameworks/lib-dynload/binascii.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			/x0jbeq8nOAy88G2tXZsaGKvvD8=
			</data>
			<key>requirement</key>
			<string>cdhash H"ff1d236deabc9ce032f3c1b6b5766c6862afbc3f"</string>
		</dict>
		<key>Frameworks/lib-dynload/fcntl.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			Ur66KXVA72xFIGKDpWAPzY+TUuM=
			</data>
			<key>requirement</key>
			<string>cdhash H"52beba297540ef6c45206283a5600fcd8f9352e3"</string>
		</dict>
		<key>Frameworks/lib-dynload/grp.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			zAD9nGxWSYygogDK+gtAGV31YWs=
			</data>
			<key>requirement</key>
			<string>cdhash H"cc00fd9c6c56498ca0a200cafa0b40195df5616b"</string>
		</dict>
		<key>Frameworks/lib-dynload/math.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			oeUERu1KzfuIe4t5eCx5wecqhb0=
			</data>
			<key>requirement</key>
			<string>cdhash H"a1e50446ed4acdfb887b8b79782c79c1e72a85bd"</string>
		</dict>
		<key>Frameworks/lib-dynload/mmap.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			hTi60arH4eo3KRab6SeCwMSAQoM=
			</data>
			<key>requirement</key>
			<string>cdhash H"8538bad1aac7e1ea3729169be92782c0c4804283"</string>
		</dict>
		<key>Frameworks/lib-dynload/pyexpat.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			UnwYzp/rcQHsDaMBgO9JubyaIBU=
			</data>
			<key>requirement</key>
			<string>cdhash H"527c18ce9feb7101ec0da30180ef49b9bc9a2015"</string>
		</dict>
		<key>Frameworks/lib-dynload/readline.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			JGRvxOcfaKOCTyzO9qf2y8CLO0E=
			</data>
			<key>requirement</key>
			<string>cdhash H"24646fc4e71f68a3824f2ccef6a7f6cbc08b3b41"</string>
		</dict>
		<key>Frameworks/lib-dynload/resource.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			BJxpyuFbv8G0wxH6wDwFfuzneb0=
			</data>
			<key>requirement</key>
			<string>cdhash H"049c69cae15bbfc1b4c311fac03c057eece779bd"</string>
		</dict>
		<key>Frameworks/lib-dynload/select.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			jMF0h0wFq7CpK3cHkVFnh7d7xZU=
			</data>
			<key>requirement</key>
			<string>cdhash H"8cc174874c05abb0a92b770791516787b77bc595"</string>
		</dict>
		<key>Frameworks/lib-dynload/syslog.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			eHQgYAoYP2d8Fv8Tvr5j6SlmKfI=
			</data>
			<key>requirement</key>
			<string>cdhash H"787420600a183f677c16ff13bebe63e9296629f2"</string>
		</dict>
		<key>Frameworks/lib-dynload/termios.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			++oYY5GXD/pvUZv03YWKexxHuVc=
			</data>
			<key>requirement</key>
			<string>cdhash H"fbea186391970ffa6f519bf4dd858a7b1c47b957"</string>
		</dict>
		<key>Frameworks/lib-dynload/unicodedata.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			1ju43d6T72hKjjHG/r5zeTh3T5c=
			</data>
			<key>requirement</key>
			<string>cdhash H"d63bb8ddde93ef684a8e31c6febe737938774f97"</string>
		</dict>
		<key>Frameworks/lib-dynload/zlib.cpython-313-darwin.so</key>
		<dict>
			<key>cdhash</key>
			<data>
			kw8gqIvj6R8I7YGBNqmgaFtV4nw=
			</data>
			<key>requirement</key>
			<string>cdhash H"930f20a88be3e91f08ed818136a9a0685b55e27c"</string>
		</dict>
		<key>Frameworks/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Frameworks/libcrypto.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			ulOZbIrPcpAy3c2QG1re56p25b4=
			</data>
			<key>requirement</key>
			<string>cdhash H"ba53996c8acf729032ddcd901b5adee7aa76e5be"</string>
		</dict>
		<key>Frameworks/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Frameworks/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Frameworks/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Frameworks/libncurses.6.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			AjUv6ku2wcBaZeuBL4TJ20U6KdA=
			</data>
			<key>requirement</key>
			<string>cdhash H"02352fea4bb6c1c05a65eb812f84c9db453a29d0"</string>
		</dict>
		<key>Frameworks/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Frameworks/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Frameworks/libssl.3.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			GVg3TvXdfZOQVqU3LYXDtD942s0=
			</data>
			<key>requirement</key>
			<string>cdhash H"1958374ef5dd7d939056a5372d85c3b43f78dacd"</string>
		</dict>
		<key>Frameworks/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Frameworks/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Frameworks/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Frameworks/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Frameworks/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Frameworks/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</string>
		</dict>
		<key>Frameworks/setuptools</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/setuptools</string>
		</dict>
		<key>Frameworks/tcl8</key>
		<dict>
			<key>symlink</key>
			<string>../Resources/tcl8</string>
		</dict>
		<key>Resources/PIL</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/PIL</string>
		</dict>
		<key>Resources/Python</key>
		<dict>
			<key>symlink</key>
			<string>Python.framework/Versions/3.13/Python</string>
		</dict>
		<key>Resources/Python.framework</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Python.framework</string>
		</dict>
		<key>Resources/Tcl</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Tcl</string>
		</dict>
		<key>Resources/Tk</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/Tk</string>
		</dict>
		<key>Resources/_tcl_data/auto.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Z74EtapMCEX4xRQxcazzGtaz8j6xoL6YEzEz6KvJgis=
			</data>
		</dict>
		<key>Resources/_tcl_data/clock.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			P9I20NjNYT3ufk7OI2OWvUJLyBZz6b1AO+5GldD/XHU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ascii.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			KUyXF1/QiUCTuGbnNUiuZgru0MPMHnOGfrZuUtNMDdI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/big5.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Rlri1IgLgAaxR2zWD6z2dodUOCRMHZOn2+TN4QNedF8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cns11643.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			CpX2f5CxzdQHp46kAyr565lvw4hkxG10tCs6f37aDIo=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1250.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			G0Lffn1rD+sXywvI2X5s5omUkjBt2IDEijnRovAnkAQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1251.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			LRvtJCLhMaFACH+vGxK4pG9947ZBO66Lw5XAbw1wubA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1252.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			FN864w6B52IL5ru3qeQgg68a4E2UzxIDVl+KPAVCrOA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1253.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			IXTZThwdWtk3F7nowgVp7ZWor1Gy06srzpnxqIcEnA4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1254.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			vEy+TJn9ZavqRfva8ozB1cQhGSgBJfu9XCwRiSrkYLI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1255.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			+L15rlqQ5TkNd9wxyzBlsPk8uIE8nmeszscuLbICegg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1256.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			u6zqgdT3o6fzwDYnOkU00x2/i2tcyivMTADLFZPPA9g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1257.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			QVFDSnFPyCIoZ3w5sHkIxOGZUvwFjibnw+urdyTODHc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp1258.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DNtZ4lXM19z0r4R8mwIK6u54zn/PXyFOvPEjMorPnyQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp437.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			1weh8DUUgG5xTwHL/LfJ+Zc6zcgMLWe71Ob4UiOlCVI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp737.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			i/yjSGmz+aOy/HGwLLrEFRKvbR+KsX0lZOZTIPiO3hA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp775.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			RPsEtccrWEtig6mbNHiWkMYntQg8XfbotberLGiQPAY=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp850.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VaotE7eJsxJfXJ0NxbbjqQ15Qm07eCXc1gT1bUxuNqI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp852.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Uma28YwxRM+tvLex0n8KfqocZB/TszkF5C5FSf03N3A=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp855.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4/BxxjrEOvZgYVBu8sV0w1979IVT+1FYrkHZIwwaEN8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp857.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			45mFxqI4CGtUQnR1UZyeAoV1BwfbUh0YIOY5cjwBw28=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp860.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			bbWROWJ9KavTbzjtLg3iprI0p9fmgcfbr4uIjxysSaU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp861.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			pDpbWL/Fe9cjsSu96p9uGpITYLNtLVLEIPNymXiEQtM=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp862.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			rqcW1JDDVDliGo8Ayn5Dl+8ccEKOIGxQNrevJfHD2C8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp863.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			E7XLSB4CFqj8KL+p0PawYM31xFez4SQ1yoJusu9SsGg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp864.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			6H7AdvlQ/NWBieNi4VBd1VsMj0+n3RqTMcXBEdLOVp8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp865.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			QH/A/gbSoFfpugEJ6pNWyrOPJ3VtE17zsGqFcFthb1A=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp866.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			gmM2Q80yZUORWsxdKKY0tXlSdM05l005VeUdczC6kzg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp869.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			J/FuPdArIhLEmA6gm9wGjPAVhKG4u5FFbAP8q6vgkx4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp874.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			PgZzY/wHZi6+UrphfCqtNkkg8q85WzQWKXQAhZrNeLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp932.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			nTPfbhz90s8lU/XidY9FfXEMr/X4xplo8mZazNbppv0=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp936.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			YZMwGSmEqA+TrG8uTl6qRj/T3dx1wfZfOXXzPg3XoLs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp949.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Jry2IEckM5YnF3EtBFl6YyZMjkREWUMlZcTBE94KJAs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/cp950.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			H7mj1S1DLqLWzUOSfOv59Y8wmiNuGxHSD+jVpfuUTm4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/dingbats.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			O96a5+r5vnmchLKqToDXi+isusoeSG8Qub3ULjrt3LI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ebcdic.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			F6fUXzuC8qQuHTaxPbXO0HeUWj6CcAlHzR+APdKmDb8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-cn.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VGOSI39H1xzuHaoarih9lNkyFqH6vWSLUPWd3OforjU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-jp.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			O/tCxNNtF2NpOu/Oh/YnehGtWnVtaR3tqATZ0O3LMJM=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/euc-kr.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			HxrUxAebM7cG6UinNajDBC9AzGgGXEjCIND1b9BIwzs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb12345.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DA3xe/7OiXodp3ZcgiRTsJhmVzAozsztE+Lv7gK8zMQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb1988.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			onF64J4M8tVmwkXcXFiJ0yZmG0DbDV2abZW45rDw51M=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb2312-raw.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			7BG/1JxxXNifudOHoHz1QmHg9KHM7BqBDgLHs4rS8oU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/gb2312.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			VGOSI39H1xzuHaoarih9lNkyFqH6vWSLUPWd3OforjU=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022-jp.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4Sko6LV1TUnQ0+eZE13itIC6hLXbqg41DZhG+mf5Q+w=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022-kr.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			asDxhFpWoaU3uabZvLck3d89Ol5hh5rpJZMbHAU0+7c=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso2022.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dT3aUYp+n23AMJchsfquWMlmH1RYAdqfBHKDkfcL4tA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-1.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dU72vzpWQiirC1bd45FSHcwabIPPuV1LdhFB5x0ujoc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-10.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			efZHDZvr0wgys6nKWc0f3KKMW+Y3O9AdlJ7uG6Uap6g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-11.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			rZOROf9xQJcL3j2RGA98TXA9f89noCxxOS3hmA3FYOQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-13.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			M8YHKgBrpOlRPXt/09CLHHRcoQebbXlsNrKlro5K4Cs=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-14.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jXNygyibr4wI7x3X5Hpsd12s5IBBnF4qktbA6Fu1s4E=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-15.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			x7A3fzDkIEhJLkcQ/loKVPqYZTlbimdI99rFO5AShPk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-16.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			xAygFLiPl65irhqBbFljse1DKnfYTYnDp2S6FciiNwg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-2.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			DgcySAM4oinMOtTN3gkCGgqBkC3G7ftfEiA+Kv9EZo8=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-3.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			czQsJ89V9iXT25DF/I5zQP/fhaUYctv7HQqMseQ+xdo=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-4.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			F3Rb3SmXeekdQdsM7ibNxxMto2ZpB6lCELWRztWlWts=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-5.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			vF7RZNFTIUBLvcrQ1kfDIv+rFllGIYLb05RUOdnsuuc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-6.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			9QLgeuPxnM3DHkNASc/HM91d+FSHwBYLAzHkAkGtAnQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-7.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			ATs7KRHGa8HKVOUQgUr0lUwxDaEHN/myokdNcUviqzk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-8.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			4dIHkXqjSD2REOJKDMDNHg5YQ8i/yQHP7npthy3ZRak=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/iso8859-9.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			HNz1EMOEZOUoTtz67DNOP8UWI2wco7mrkcqHjCOGaRQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0201.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			SA9h0OGnXe5Zv5pm3gu3j6rk6H/WMX+TSAQSEjJ31EI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0208.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			EY6hYO8p4RtG3sV68sREBZNN2KfEnSvIuQyU6LqmE4s=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/jis0212.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			JKnTef2jnyvMBYDKPgvS6Zriea9eKEHJ59vn+THRnMA=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-r.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			cUKxEguZPWCRGXV0CQ/gS+PqZP/DrVoWekteC0LJ8GI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-ru.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			CH9ZfSI6CDUmKw+5Y5JGKPxzFrot/7KLc8H+DbAB9TE=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-t.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			tUIV7d3L88K6E8tLoW+poVKgNbdaSqz936RtFYHy20I=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/koi8-u.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			y68OZCbI0XZoFVKMzxCkOLHz5MWxBXYdo66BCglPKmI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/ksc5601.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			D4tTCtDey/jdgdqCkbiw+XbGQ7WiktuEaAsx7Pvl0Ao=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCentEuro.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jzCJ9LLKR7esTLeDdbK/rAEmgROnxn0CD4tbfywlu9o=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCroatian.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			jQtqiCt0LFzOk4JBMoYGwRHdoMuDM06+3NoXYF82Qa4=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macCyrillic.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			6xNaiVGfLgBCgt7SGxHDr3zLIyDJdy8t99GkobZ05JE=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macDingbats.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			j7zGPLKJr6rhW0OHUsF0b0E/O3m6WEXC71K6EQT4vaY=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macGreek.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			97/5gije2YHsmk0dDaYiR6jSPxWJJuOsvsPM43nJmMI=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macIceland.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			Yz9ePnW/FZDJSrnL81ONDwp6MZ25AWmTkIRS2QPZxP0=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macJapan.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			KmhWKY7GKaFr3ZJHEd/j87HjqILd8EtzEHhdg+wNVmw=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macRoman.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			aPIrrTDaqBshWSVBbBzIM2Czu4fvw0IFiSlzGsZ4/zc=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macRomania.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			ca6ArftDe3vIjzx2/TcHREmzUm56pXdtK5/VpDwGb6g=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macThai.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			zq1esrC0TvQAP7yy5JygUDmSuh1lQNEay7uE/bvW55o=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macTurkish.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			9wO390zG9fqpWfUcdXyUYjZ34nATvK4jvvugGjkmRtk=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/macUkraine.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			TJTn++GDN5gFBW2WCrYk14h55DJ4Ji5Na5ireOX+/qg=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/shiftjis.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			dQJYfVLngQIo8uy0WsQxnqD1wAi3rJEFO5IAENxt35Q=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/symbol.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			c/0rXhQwnYwDbTNPE3ue3x97MtvUVJHPkxhIGFgtBnE=
			</data>
		</dict>
		<key>Resources/_tcl_data/encoding/tis-620.enc</key>
		<dict>
			<key>hash2</key>
			<data>
			WRi14d7wl4Hv0odRtog2ZaruKfHSRPCJHt7Nqb9qS2M=
			</data>
		</dict>
		<key>Resources/_tcl_data/history.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			4uuL3zlzLKSBc+YdmtpPDQld2qPtH8ziHApSDHVJPY8=
			</data>
		</dict>
		<key>Resources/_tcl_data/http1.0/http.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			8RXZ0r5Yhhdf5hBITqrI4X2f6xxx1BmvZsbjGwwhVSM=
			</data>
		</dict>
		<key>Resources/_tcl_data/http1.0/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QgxLMIjJ2s0hvDSAEcrGHXyyg7m+54rnLu12SrCUZRw=
			</data>
		</dict>
		<key>Resources/_tcl_data/init.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			c5Yp7FQiQdIBaeiIo7uYMFLEaRbCjDj8kSapr+itYeQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/opt0.4/optparse.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			BPSy802VnP9HxlAqE30VrJ3E9U1KbnhHj2TIx+FISag=
			</data>
		</dict>
		<key>Resources/_tcl_data/opt0.4/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			xu8AxVNieCo7jBhehEEyJd3mTOxpD7MkFY1wKMU8N0U=
			</data>
		</dict>
		<key>Resources/_tcl_data/package.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qzzIxPrjgxdhz3flAQKKSNtuT0/ZmBAnhef8p8nnoUI=
			</data>
		</dict>
		<key>Resources/_tcl_data/parray.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			6+WitMu81/0/em921o14VjAdsBs1DAQJQqe4BqRuABQ=
			</data>
		</dict>
		<key>Resources/_tcl_data/safe.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			vXBUjqIJATWRjy5ho5v91hY+yM74L0AM4iCh7cS+3Ww=
			</data>
		</dict>
		<key>Resources/_tcl_data/tclAppInit.c</key>
		<dict>
			<key>hash2</key>
			<data>
			UAbqOVwsqW50NT6Uhta+PiqFp5JZaaT+QWYvCS75TKo=
			</data>
		</dict>
		<key>Resources/_tcl_data/tclDTrace.d</key>
		<dict>
			<key>hash2</key>
			<data>
			j1Kh00NqHgN5UUIBU7j6Ugnz0NfcnZ2n/IBuDx03hvU=
			</data>
		</dict>
		<key>Resources/_tcl_data/tclIndex</key>
		<dict>
			<key>hash2</key>
			<data>
			52IMPCnEl/bAEZtjsn2V352k19OkeL7xOPtFsaOUUqM=
			</data>
		</dict>
		<key>Resources/_tcl_data/tm.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			SyargiTT5o0QsJW4E02CAfri6JNjXa52gjfdZm+nZR8=
			</data>
		</dict>
		<key>Resources/_tcl_data/word.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			8Y6K4pqo7Sviy7VWi0JD9lT3+wqiBm4Wl3hj7hQVubw=
			</data>
		</dict>
		<key>Resources/_tk_data/bgerror.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ZESFUunddehLjfY1KeZ/ltuTUdSRPsLpb9Up/kw14Fs=
			</data>
		</dict>
		<key>Resources/_tk_data/button.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			LwE7ZD1i8I3aqh3qOf+A1mB1acnhrMGUBjd7ZNdcz1M=
			</data>
		</dict>
		<key>Resources/_tk_data/choosedir.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			NR84nwkNDP5bEiL4N4VjCh7q5U9oJ0d4RYBbuz0Rn3w=
			</data>
		</dict>
		<key>Resources/_tk_data/clrpick.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			xVB5xDQmWXvoNsi43tBKvauOHNfSuQqJivlSD4AsYEA=
			</data>
		</dict>
		<key>Resources/_tk_data/comdlg.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			9izluEUK57PsK92f32fHf8yZqhjeGV3lM3x1yZeksRw=
			</data>
		</dict>
		<key>Resources/_tk_data/console.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			fhpaiCt966bhPpUEh6w5FS8keTBaQZceuSobBtVvrqk=
			</data>
		</dict>
		<key>Resources/_tk_data/dialog.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			0bHcykYo9h6hUqD6aCAXX2E7w9bpK3OdATKB20huYl0=
			</data>
		</dict>
		<key>Resources/_tk_data/entry.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			kvsv5bIsxpf5OW02gc89McA1mNfY0YWLS3q5dunIClw=
			</data>
		</dict>
		<key>Resources/_tk_data/focus.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QNThAaZLdTYfdjR5sBIHrnFTUzfnnObhYiZYQvZHHu0=
			</data>
		</dict>
		<key>Resources/_tk_data/fontchooser.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			iqfPB4wm85bhpCGgPQ89BXMeQT/USHUOGS2r/Tqrc8U=
			</data>
		</dict>
		<key>Resources/_tk_data/iconlist.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Cwh+IFJg1eJUWVpBi6LwnbtV6WefoOjPqZR6eSFDfvw=
			</data>
		</dict>
		<key>Resources/_tk_data/icons.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			TKuArOQQSkFpyb0f2yTWl4ibmEuRopC2mkgZEsOKn9Q=
			</data>
		</dict>
		<key>Resources/_tk_data/images/README</key>
		<dict>
			<key>hash2</key>
			<data>
			JpWt/46QDDG02GQU0iuKSdbdhlyj3Zlnj6NVzcRgk6g=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo.eps</key>
		<dict>
			<key>hash2</key>
			<data>
			8+d/2UGY7EeDEJNVU2Y46RYvnFeUdTgwdNAkA30Xl9M=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo100.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			cvazTTyPQk/wopCnk/z780/VYwqRbNAuCl3aAUS1lX8=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logo64.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			E4wkA4IwTzUDg7Au1WxpEDqUMcBUTrHsXc197HpVXdk=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logoLarge.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			D0BHZNB6auLvnh4OjqrCeLfUiNYc8cCEFG8vM7SF8u0=
			</data>
		</dict>
		<key>Resources/_tk_data/images/logoMed.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			TQvTIoq0zD5RWfQze+lp7HtzNOJlyZt2M+Pa88P8+2I=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo.eps</key>
		<dict>
			<key>hash2</key>
			<data>
			KUTrxK8YlJUb+fElD05u34EcIYN0WVDqmoqSZxWILPc=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo100.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			vMDmRYJJQz6MumxYEit8DvqVV8vI+1+Tku7V0lefxws=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo150.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			X8JcMK7nZHfxxOkikxzIBoI98FlSVYP/VwVwXZ6RPBw=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo175.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			YoZulVAcQ2symhVDI1V0PG79ZKN8+2W87ORlq2Ps8kA=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo200.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			utkRY4Y0P0pMOUvbhxRuSfZ09ofVK7hHvZ6BmP2jgsw=
			</data>
		</dict>
		<key>Resources/_tk_data/images/pwrdLogo75.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			RiqP+P0FGoEA6MbAhvSX5AVqzlsgtEeR9Kq5ZLAQpEg=
			</data>
		</dict>
		<key>Resources/_tk_data/images/tai-ku.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			5Tj49JNMpuHOKUFtKSFx8o5n2mxy7Z0ja6QvN0RepB4=
			</data>
		</dict>
		<key>Resources/_tk_data/listbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			/f1M8xAtg7rTVW0OAIa18yD3RIS6v5BCHPUm/YufvrA=
			</data>
		</dict>
		<key>Resources/_tk_data/megawidget.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ZVQxBBVDj0dyKm14mqig/8z0xfaZQSwtacMs/+bRnPQ=
			</data>
		</dict>
		<key>Resources/_tk_data/menu.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Oek6K9dbkVYjXzJx6gL9fKyCtUKuVt9zPNe9YqLEIH4=
			</data>
		</dict>
		<key>Resources/_tk_data/mkpsenc.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			CsnRHUBG702ObSGfaUG/acauRIxqHC9/w4L4S1eG9mA=
			</data>
		</dict>
		<key>Resources/_tk_data/msgbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			mmKB+woZJ9e4H86ev8lSNb2I3xFK2Kh6/qjqawlTM4o=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/cs.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			0bD+0L6lGz+vCNhjQDTHOIvnFI+bgHRgt9GFcG24QW8=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/da.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			hebO5gAZJzdnJfkeqlXRez2eOGQ+F3VaQsBf5JHGO94=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/de.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Bt11cmJt9csKjTr/usm7dMsSRpB2g21m/RmuW1+rQsc=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/el.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			HVbQp8B9NLuBZcukf6STUbi8Wp2yRCkLlgHFiF0WFVw=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/en.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Zzx2pIraCaFUywOFNL+Q47nAul/WsWGdszUH3mVVM2I=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/en_gb.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			+5PUVanZzz+CLJaN+yc+2THkM/JJTXHWtfjYPd5+rMI=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/eo.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yyS6lZhmcXdqg9zwJWJjgJBx0z7dnAY4Oxn0w2+CCTM=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/es.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			kdxHGNyFZsNuS80MKSwB9GfKdmHv9gG4cKvN/kqU7Ls=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/fi.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			9KLE2npnectQiwq7w988lKz3cZt7Ne31VSha24+PzFs=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/fr.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			Cou7TR/Ye/epDd+lD0ckmUyc540fPpHPQMEXfbeUHcU=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/hu.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			yBNOrRKeROnFBD4drYGmqQDw3nHbNGjiYDhAA4aH8dg=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/it.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			n4PdAwntYhEA8xh//NrlC3X1lzu+dK9VCnjvABBJXe0=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/nl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			09B6rXksDoP0cEswSTHqVJ0Sy7PZmlc9mBXpVKVxBwc=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/pl.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			GZO07C3ACdLmyhhdC9Vl0/M6Tvp5uso55Pl/V01j8wU=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/pt.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			rQ5GYTHTeJ3jIdnQWI4Z5GR7qC7eQe7m6+9GR4b4vb4=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/ru.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			BhkL8kYjafatTMuN7l9ln4SFbOzPKAbd35UR4BU5DCM=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/sv.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			t26/ohvB6TegSgTlEivmS1ze4fR8cFi3HYuSPXDDsXs=
			</data>
		</dict>
		<key>Resources/_tk_data/msgs/zh_cn.msg</key>
		<dict>
			<key>hash2</key>
			<data>
			TVsyB7WkBymheinas89+5j5T+cB90FlKvwr4OioBoXg=
			</data>
		</dict>
		<key>Resources/_tk_data/obsolete.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			9pKaXg0YvExmZiBsY6xKqmbtxLn0Vt/AgzAM+pWkS80=
			</data>
		</dict>
		<key>Resources/_tk_data/optMenu.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			dtvb+SFmeNSNFkD4/R4njnFASC4crHaAEnqaQlzGHe4=
			</data>
		</dict>
		<key>Resources/_tk_data/palette.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			YLhXk2i7MGPxbSXwBzhREeDvjZe7KWsDZW3BduNR48o=
			</data>
		</dict>
		<key>Resources/_tk_data/panedwindow.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			St9zixdpFInHHEudmmSxKWGtqGZ7gYVvetvGHf/q3yk=
			</data>
		</dict>
		<key>Resources/_tk_data/pkgIndex.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			HVX+cWUunjO6PHx5Pm6zJeDlTnbPgFoNEH1Bae2vnwk=
			</data>
		</dict>
		<key>Resources/_tk_data/safetk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			3bDNWcaPwKwhmQCgTfDMtXZEDdiBLEG8oguXRYCGCJI=
			</data>
		</dict>
		<key>Resources/_tk_data/scale.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			QjVGkzJp+uyRMAHyvOMLvbzjkn3J3Zb+dIE+f/snz7U=
			</data>
		</dict>
		<key>Resources/_tk_data/scrlbar.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			GJ5+5LZ4YQAccUpViA2zSs99YmqBbhiwSyMq+ebjPoE=
			</data>
		</dict>
		<key>Resources/_tk_data/spinbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qATIMCngTmvTTTNSYPOIiQ12V961RBc4WL0FAY1zqwE=
			</data>
		</dict>
		<key>Resources/_tk_data/tclIndex</key>
		<dict>
			<key>hash2</key>
			<data>
			5+qDzV3zIvt2DJeHye2pK609BIc3vijSyWBrkkPPC7s=
			</data>
		</dict>
		<key>Resources/_tk_data/tearoff.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			eTDIeB1kOMEeOh8PPTwdBS6SS9+3/ef8F636uc4+QQI=
			</data>
		</dict>
		<key>Resources/_tk_data/text.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			/RK8o8fc7fP8ZkCQKvHJ9IvF3yfJFT7a7BVBPxWB5Kk=
			</data>
		</dict>
		<key>Resources/_tk_data/tk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			wjqp6TfK9j92DQWgjwP9aozPPWJzTg2y4jrfgxZmpCk=
			</data>
		</dict>
		<key>Resources/_tk_data/tkAppInit.c</key>
		<dict>
			<key>hash2</key>
			<data>
			2dmLyZ2Z0KmIOrUFTe1Rmrf+Rx4NHSRgpUN/I1rIyVE=
			</data>
		</dict>
		<key>Resources/_tk_data/tkfbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			tYHxUjBBC/jYxr36NppiZw6JtVYqIhcVaxk7PORnIVM=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/altTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			6Ve3k9TmCNee02o08iOQtBqtd3rm7MjMvpaqMK7QKZ0=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/aquaTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			MPMqHxB942195JKZolQ2jUwYAbDN3yWSvdkxFr0pZ6Y=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/button.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			aE7UCpYJyidc5vPlR/vymA48BA+t2vD9ObfL19WYc2Y=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/clamTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			UGA1bGydhhGKLTUdLBO63o8N4+cG0SImtpjoqkUxt1w=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/classicTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			aARyAVwfqIiSYYfSTunT/wNqQOUuWNr0eIYxe7dNtv4=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/combobox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			aoWfWH73gfo0g2jaw8rWzO9yftUaJRCIirwq+LyIrnY=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/cursors.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			wduT6vSCvuG/V8tCPyioxncM7Zs367uMn8htYSFf6Og=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/defaults.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			3ncV4cG395SQLzCu/LK4ewYnnANfYdE6RdTddvMO2rA=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/entry.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			nDQXNeF5J5dmJA3Im4O2qeusYIkAZSUf5c9Dj4sU2Tc=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/fonts.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Hy0pZbLHKulTwIKaInV3raiVnSRQJjizGGqoQLZ4zDo=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/menubutton.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			oW37Hq93sFoO+StMx9APVZ+2zwsvc1w6+sUDA8UkoIE=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/notebook.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			i++TNTpxNENNx4Dup3tS723IU6GT6NgaEy0OJO2TI0s=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/panedwindow.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			LRLU3zx7KYji4oAXNy61YugUNsYCuLsxxxe90O7BinQ=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/progress.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qcNPWV5UfOlO5l4nxBUZXSshBlOp/8+zlVnF4PqcBvg=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/scale.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			YWHEC/XWz+nINqqyNw78lRvD4UvBfdlHdFW4a1JS1pY=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/scrollbar.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			ByarHlQbNXE+b3uR+rtP7VwSm4Fg1NXb+Frv+qIY1Us=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/sizegrip.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			NvrwMr2ccvMHUf6wB3j9IdARCM2x8+pK/QtbtehHMt4=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/spinbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			UTLjkRKRZIbEkBjBv/unDzDfYHsQPiKuXUPHHavoy0A=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/treeview.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			Qvy3Awa4rUg/omSdfJsTiRsrhB4m0pT1PaMi2kiMYlQ=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/ttk.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			WGicA1FNGXsus8TM8ZheSPN/IDQ8GOsXneLX9wmbIPM=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/utils.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			qwchxQyscf8853JpQKrDOQtyMiTNQhrJ6rBqVS8aoVI=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/vistaTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			CKsS8MbJbKxSTAx1JPjj8z9z686X6NTXEX3QoFBuMZo=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/winTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			522J8DLSz/Eiw4Xg5SHCmZRq0U9f/v288XZiC0RMDaI=
			</data>
		</dict>
		<key>Resources/_tk_data/ttk/xpTheme.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			poyLNNJVaWdOHOv1Sz9gx7PA+u0qovfTXczLzVGxAbY=
			</data>
		</dict>
		<key>Resources/_tk_data/unsupported.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			402CjnQPFRuWAik0qux7uDQ+I9BA+1TARkGIj1F2frg=
			</data>
		</dict>
		<key>Resources/_tk_data/xmfbox.tcl</key>
		<dict>
			<key>hash2</key>
			<data>
			IdJZTIMZ8UxHoPC/PSrKvMgsmrjYSmT6PLYIx+dfWcM=
			</data>
		</dict>
		<key>Resources/base_library.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			cHjx/5VFxqyOr4iQ3/2iz7+0Z8oplIwfT2y0YG0O8X8=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/CustomTkinter_shapes_font.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			+tZ+KwYMMYtshkbQh/vTrdk4tmdiQ/FLDFJiMXlkEnQ=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			SsjgNgb/pMN/YaZRCiCA8fN6cFT0cmwhSIfTsj9y42k=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/fonts/Roboto/Roboto-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			MZz/bnox8PKkHEddykKJCqXRn+FgF+IpD4wdThT3ZIE=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/icons/CustomTkinter_icon_Windows.ico</key>
		<dict>
			<key>hash2</key>
			<data>
			EjTAF8hx6y4g029mj5PgZs3Lk9tGTVzvnXpb+DUG0ow=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yUF0cMFs7XpD1sSo4Cevpu3GLCTVrufEwtzRE4WWTTs=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/dark-blue.json</key>
		<dict>
			<key>hash2</key>
			<data>
			alf6b4+4lhowzmQpUisYDXbjr5uODarCWQWYQThqa9M=
			</data>
		</dict>
		<key>Resources/customtkinter/assets/themes/green.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ea2Gv+p/BVesHiCAKJKrtEqWevFbkxWwA5zXXItyp3Y=
			</data>
		</dict>
		<key>Resources/img/123.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			zRbt2eaNE2k5bwMRPx6qaxe5d8M6NSKc7Qjkbrb/ODY=
			</data>
		</dict>
		<key>Resources/img/bluetooth.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			AXDfQ7PFxvX7gGQYsojZ3L4PMUU0IIMf0Vbora632Mc=
			</data>
		</dict>
		<key>Resources/img/device.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			/wu7jVkhEKGPoKTYum8zEAUTPQuFfFMXP41QTSed88g=
			</data>
		</dict>
		<key>Resources/img/erase.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7l2sY7T3qXSjQZXIEDtzavqbz2NSrlH4itxtn6/igJc=
			</data>
		</dict>
		<key>Resources/img/exit.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b5d6vlIoz/sJxS4j+nsUsX46dyNReM1kJkVz+QSIJJ8=
			</data>
		</dict>
		<key>Resources/img/findmy.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			6O5TLJgtkHq7LHYtkdu2JJQppDEmUUKnPrSZurSZ3Ew=
			</data>
		</dict>
		<key>Resources/img/shutdown.png</key>
		<dict>
			<key>hash2</key>
			<data>
			adYIbIXC8nbpXDrEmKQUQo+zPZY5R8nBZJbzWaxWFPc=
			</data>
		</dict>
		<key>Resources/img/sysinfo.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			vZn9xKNKjSY5I0arOZQ4oRzT4WN8azNInwkwJN8dE2k=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vs_mac_tool_v2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CD+ufxpcMTBVE8LJvKzniszXAhS8xNABGEo6MHqbwPU=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vsmactool.jpeg</key>
		<dict>
			<key>hash2</key>
			<data>
			pP6U2IyrwYxqXce/NqNnp6uZ/AyYydNiJlw13ZKwobE=
			</data>
		</dict>
		<key>Resources/img/vs_icns/vsmactool2.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			lrPqU4byxTwkOHY4msSrYvfC+sUcrU7P0gZ68v7Cn2E=
			</data>
		</dict>
		<key>Resources/img/vsmactool.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			lFOvWLdflW1tU5HMS0wAl+vrdmwSApw2D5zRlEO8wzE=
			</data>
		</dict>
		<key>Resources/img/vsmactool.png</key>
		<dict>
			<key>hash2</key>
			<data>
			T6gwnbchNQ4tJnLwNrEfUyvbOCLS0b4Y4XuTlCJLB7U=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			htoPAa6uRjSKPD1GUZXcHOzN55956HdppkuNoEsqR0E=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			rS2xXSTtNujJISr88RqlBhI4T0Wvx4yWyQLTSGSZ2tM=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			LfiRvYvKYyfZ9nnJLOFnA1g3sI2HE9OptsURFqvtwAs=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			PZUExdf71Ui/so67QXpySuHtCi3+J3wvF4ORK6k/S8U=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/entry_points.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			8ibyc9zH2ST1JDZHWlQZHEUPx9kVaXfVy8z5af/6OUk=
			</data>
		</dict>
		<key>Resources/keyring-25.6.0.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			ohh1dke28/NdSNkZ6nkVSwIKkLJTOwIfEwnXKva3pkg=
			</data>
		</dict>
		<key>Resources/lib-dynload</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/lib-dynload</string>
		</dict>
		<key>Resources/libXau.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libXau.6.dylib</string>
		</dict>
		<key>Resources/libcrypto.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libcrypto.3.dylib</string>
		</dict>
		<key>Resources/libjpeg.62.4.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libjpeg.62.4.0.dylib</string>
		</dict>
		<key>Resources/liblcms2.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblcms2.2.dylib</string>
		</dict>
		<key>Resources/liblzma.5.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/liblzma.5.dylib</string>
		</dict>
		<key>Resources/libncurses.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libncurses.6.dylib</string>
		</dict>
		<key>Resources/libopenjp2.2.5.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libopenjp2.2.5.3.dylib</string>
		</dict>
		<key>Resources/libsharpyuv.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libsharpyuv.0.dylib</string>
		</dict>
		<key>Resources/libssl.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>../Frameworks/libssl.3.dylib</string>
		</dict>
		<key>Resources/libtiff.6.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libtiff.6.dylib</string>
		</dict>
		<key>Resources/libwebp.7.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebp.7.dylib</string>
		</dict>
		<key>Resources/libwebpdemux.2.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpdemux.2.dylib</string>
		</dict>
		<key>Resources/libwebpmux.3.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libwebpmux.3.dylib</string>
		</dict>
		<key>Resources/libxcb.1.1.0.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libxcb.1.1.0.dylib</string>
		</dict>
		<key>Resources/libz.1.3.1.zlib-ng.dylib</key>
		<dict>
			<key>symlink</key>
			<string>PIL/.dylibs/libz.1.3.1.zlib-ng.dylib</string>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER</key>
		<dict>
			<key>hash2</key>
			<data>
			zuuue4knoyJ+UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA</key>
		<dict>
			<key>hash2</key>
			<data>
			anuQ7/7h4J1bSEzfcjIBakPi2cyVQ7y7jklLHsBeH1k=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD</key>
		<dict>
			<key>hash2</key>
			<data>
			DY08buueu+hsrH1ghhVSQzwynanqUSSLYdAr4uXmQDA=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED</key>
		<dict>
			<key>hash2</key>
			<data>
			47DEQpj8HBSa+/TImW+5JCeuQeRkm5NMpJWZG3hSuFU=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL</key>
		<dict>
			<key>hash2</key>
			<data>
			mguMlWGMX+VHnMpKOjjQidIo1ssRlCFu4a4mBpz1s2M=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			CO3fD9yylANiXkrMo4qHLV/mqXL2sC5JFKgt1yWAT+A=
			</data>
		</dict>
		<key>Resources/setuptools/_vendor/jaraco/text/Lorem ipsum.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			N/7c/79zxOufBY9HZ3yzMgOkNv+TkOTTio4BydrSjgs=
			</data>
		</dict>
		<key>Resources/tcl8/8.4/platform-1.0.19.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			zHmOsmZZyxJGnPkfLnlVW0SXPdZwe/6/eqK52Y2AaoI=
			</data>
		</dict>
		<key>Resources/tcl8/8.4/platform/shell-1.1.4.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			PpoTm30tdoqLlDJ/60AsT8ev7aFW5Gqy0i6MQCZhQqU=
			</data>
		</dict>
		<key>Resources/tcl8/8.5/msgcat-1.6.1.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			VzKVGwK0XDcCwvnufEPeq/z+Cqrok/ibzFnjK9lETOM=
			</data>
		</dict>
		<key>Resources/tcl8/8.5/tcltest-2.5.9.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			WHxo+aEbfWi0gWBaCG0r7rGgYKAfTsRxR6R6953HeGM=
			</data>
		</dict>
		<key>Resources/tcl8/8.6/http-2.9.8.tm</key>
		<dict>
			<key>hash2</key>
			<data>
			v2zRGP2uc+G2j6VUu12WGk3Du24HJLZ3muQBRvCJM3k=
			</data>
		</dict>
		<key>Resources/vsmactool.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			lFOvWLdflW1tU5HMS0wAl+vrdmwSApw2D5zRlEO8wzE=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
