# Last Updated:08-07-2025 - v2.2.9 - Apple Silicon Compatible Version
import sys
import os
import traceback
import logging

# Suppress tkinter deprecation warning
os.environ['TK_SILENCE_DEPRECATION'] = '1'
###################### Working Pyinstaller command: ######################
# pyinstaller --name="Mac-AppleSilicon-Checker_0807" --windowed  --icon=img/vsmactool.icns  --add-data "img:img"  --clean --noconfirm  --collect-all customtkinter  --collect-all PIL  mac_AppleSilicon_checker_v2.2.9.py
# pyinstaller --name="Mac-AppleSilicon-Checker" --windowed  --icon=img/vsmactool.icns  --add-data "img:img"  --clean --noconfirm  --collect-all customtkinter  --collect-all PIL  mac_AppleSilicon_checker_v2.2.9.py

# Set up basic logging first, before any other imports 
log_dir = os.path.expanduser("~/Library/Logs/vs_mac_security_checker")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, "vs_mac_security_checker.log")
logging.basicConfig(
    filename=log_file,
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
def log_error_and_exit(error_msg, exc_info=None):
    logging.error(error_msg)
    if exc_info:
        logging.error(traceback.format_exc())
    sys.exit(1)
try:
    logging.info("Starting application initialization")
    import socket
    import subprocess
    import customtkinter as ctk
    import threading
    import time
    import csv
    import xml.dom.minidom
    import xml.etree.ElementTree as ET
    from tkinter import filedialog
    from datetime import datetime
    import queue
    from PIL import Image
    import platform
    from password_dialog import get_sudo_password
    # Try importing keyring, but don't fail if it's not available
    try:
        import keyring
        KEYRING_AVAILABLE = True
        logging.info("Keyring module successfully imported")
    except Exception as e:
        KEYRING_AVAILABLE = False
        logging.warning(f"Keyring module not available or failed to load: {str(e)}")
        print("Warning: keyring module not available or failed to load. Some features may be limited.")
    # Disable Tkinter menu bar creation which causes crashes on macOS vs
    os.environ['TKINTER_NO_MACOS_MENUBAR'] = '1'
    logging.info("Environment variables set")
    # Force dark mode for all CustomTkinter windows
    ctk.set_appearance_mode("dark")
except Exception as e:
    log_error_and_exit(f"Failed during initial imports: {str(e)}", exc_info=True)
def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = getattr(sys, '_MEIPASS', os.path.abspath('.'))
        full_path = os.path.join(base_path, relative_path)

        # Verify the path exists, if not try alternative locations
        if not os.path.exists(full_path):
            # Try relative to script directory
            script_dir = os.path.dirname(os.path.abspath(__file__))
            alt_path = os.path.join(script_dir, relative_path)
            if os.path.exists(alt_path):
                return alt_path

            # Log the issue but return the original path
            logging.warning(f"Resource not found: {relative_path} (tried {full_path} and {alt_path})")

        return full_path
    except Exception as e:
        logging.error(f"Error getting resource path for {relative_path}: {str(e)}")
        return os.path.join(os.path.abspath('.'), relative_path)
# Default passwords
SUDO_PASSWORD = "1111"  # Default sudo password, will try this first
WIFI_PASSWORD = None  # WiFi password for automatic connection
# Check macOS version
def get_macos_version():
    """Get the macOS version as a tuple of integers (major, minor, patch)"""
    version_str = platform.mac_ver()[0]
    version_parts = version_str.split('.')
    # Ensure we have at least 3 parts, padding with zeros if needed
    while len(version_parts) < 3:
        version_parts.append('0')
    return tuple(int(part) for part in version_parts[:3])
# Get macOS version
MACOS_VERSION = get_macos_version()
def check_compatibility():
    """Check system compatibility: macOS 10.15 or newer"""
    version_str = platform.mac_ver()[0]
    version_parts = version_str.split('.')
    major = int(version_parts[0])
    minor = int(version_parts[1]) if len(version_parts) > 1 else 0
    if major < 10 or (major == 10 and minor < 15):
        print("This application requires macOS Catalina (10.15) or newer.")
        sys.exit(1)
    try:
        chip = subprocess.check_output("sysctl -n machdep.cpu.brand_string", shell=True, text=True).strip()
    except Exception:
        chip = "Unknown CPU"
    print(f"Running on: {chip}, macOS {version_str}")

# Call compatibility check at startup
check_compatibility()

APP_NAME = "Mac Apple Silicon Checker v2.2.9"
APP_VERSION = "2.2.9"

class InternetCheckerApp(ctk.CTk):
    def __init__(self):
        print("DEBUG: Entering InternetCheckerApp.__init__()")
        try:
            print("DEBUG: Before super().__init__()")
            logging.info("Initializing InternetCheckerApp")
            super().__init__()

            # COMPREHENSIVE FRAMEWORK FIXES FOR RECTANGLE ARTIFACT
            # Force CustomTkinter appearance mode BEFORE any configuration
            ctk.set_appearance_mode("dark")
            ctk.set_default_color_theme("dark-blue")

            # Configure window with multiple artifact prevention methods
            self.configure(fg_color="#2b2b2b")  # Slightly lighter for visibility
            self.configure(corner_radius=0)  # Remove rounded corners that might cause artifacts

            print("DEBUG: After super().__init__()")
            logging.info("Base class initialized")

            print("DEBUG: Setting window title")
            # Set app name in title bar and macOS app switcher
            self.title(APP_NAME)
            # Add About to the default macOS app menu (no custom app menu)
            self.createcommand = self.createcommand if hasattr(self, 'createcommand') else self.tk.createcommand
            try:
                # This will add About to the default app menu on macOS
                self.createcommand('tk::mac::ShowAboutDialog', self.show_about_dialog)
            except Exception:
                pass
            
            print("DEBUG: Setting up network config")
            # Network share configuration
            self.network_path = "/Volumes/Share_IT"  # Match Finder's default mount point
            self.server_address = "************"
            self.share_name = "Share_IT"  # Lowercase 's' to match SMB URL
            
            # Add network_config dictionary with credentials (Windows share, no password required)
            self.network_config = {
                "mount_point": self.network_path,
                "server": self.server_address,
                "share": self.share_name,
                "username": "ituser",
                "password": ""  # No password required for this share
            }
            print("DEBUG: Network config set")
            
            logging.info("Setting up UI components")
            print("DEBUG: Loading icons")
            # Load icons from the img folder using resource path
            try:
                self.icons = {
                "bluetooth": self.load_icon(get_resource_path("img/bluetooth.icns"), (20,20)),
                "findmy": self.load_icon(get_resource_path("img/findmy.icns"), (20,20)),
                "profile": self.load_icon(get_resource_path("img/device.icns"), (20,20)),
                "system": self.load_icon(get_resource_path("img/sysinfo.icns"), (20,20)),
                "shutdown": self.load_icon(get_resource_path("img/shutdown.png"), (20,20)),
                "erase": self.load_icon(get_resource_path("img/erase.png"), (20,20)),
                "exit": self.load_icon(get_resource_path("img/exit.png"), (20, 20))
                }
                print(f"DEBUG: Icons loaded: {self.icons}")
            except Exception as e:
                logging.error(f"Error loading icons: {str(e)}")
                print(f"DEBUG: Error loading icons: {e}")
                # Create empty icons dict as fallback
                self.icons = {
                    "bluetooth": None, "findmy": None, "profile": None,
                    "system": None, "shutdown": None, "erase": None, "exit": None
                }

            logging.info("Setting up window properties")
            print("DEBUG: Setting up window properties")
            # Set window properties - try to eliminate rendering artifacts
            self.title(APP_NAME)
            self.geometry("1120x755")
            self.minsize(800, 300) # Adjusted minimum height for new layout
            self.maxsize(2000, 850) # Adjusted maximum height for new layout

            # Try additional properties to eliminate rectangle artifacts
            self.attributes('-alpha', 1.0)  # Ensure full opacity
            self.update_idletasks()  # Force immediate rendering update

            # Remove problematic after calls that cause crashes

            print("DEBUG: Configuring main window grid")
            # Configure main window grid (self)
            self.grid_columnconfigure(0, weight=1)  # Only one main content column that expands horizontally
            self.grid_rowconfigure(0, weight=1)  # Top content area expands vertically
            self.grid_rowconfigure(1, weight=0)  # Status bar row fixed at bottom (no vertical expansion)
            
            logging.info("Creating main frame")
            print("DEBUG: Creating main frame")
            # Create main frame as the primary content container for the top part of the window
            self.main_frame = ctk.CTkFrame(self, fg_color="#3a3a3a") # Visible dark background
            self.main_frame.grid(row=0, column=0, padx=(0,0), pady=1, sticky="nsew")
            
            # Configure main_frame grid (internal layout for left and right panels)
            self.main_frame.grid_columnconfigure(0, weight=0) # Left panel column (fixed width)
            self.main_frame.grid_columnconfigure(1, weight=1) # Right panel column (expands horizontally)
            self.main_frame.grid_rowconfigure(0, weight=1)    # Single row for content, allowing right panel to expand vertically
            print("DEBUG: Main frame created")
            
            logging.info("Creating Left Panel Frame")
            # Create Left Panel Frame to hold Process, System Info, and Shortcuts frames
            # Use regular CTkFrame instead of CTkScrollableFrame to eliminate scrollbar rectangle
            self.left_panel_frame = ctk.CTkScrollableFrame(
                self.main_frame,
                fg_color="#333333",  # More visible background
                width=260,
                height=500,
                border_width=1,
                border_color="#666666"
            )
            self.left_panel_frame.grid(row=0, column=0, padx=(0,0), pady=0, sticky="nsew")
            self.left_panel_frame.grid_columnconfigure(0, weight=1)
            logging.info("Creating Process Steps Frame")
            # Process Steps Frame with macOS-style border - FIXED HEIGHT
            self.process_frame = ctk.CTkFrame(
                self.left_panel_frame,
                width=280,
                height=215,
                fg_color="#444444",  # More visible
                border_width=1,
                border_color="#777777",  # Brighter border
                corner_radius=8
            )
            self.process_frame.grid(row=0, column=0, padx=(0,0), pady=(0, 1), sticky="ew")
            self.process_frame.grid_propagate(False)
            self.process_frame.grid_columnconfigure(0, weight=1)
            logging.info("Creating Process Steps Header")
            # Process steps header
            self.process_steps_label = ctk.CTkLabel(
                self.process_frame,
                text="Security Lock Checks",
                font=("SF Pro Display", 16, "bold"),
                text_color="#FFFFFF"  # Pure white for visibility
            )
            self.process_steps_label.grid(row=0, column=0, padx=20, pady=(1, 0), sticky="nsew")
            logging.info("Creating Buttons and Checkboxes")
            # Define buttons and their corresponding functions
            self.buttons = [
                ("Check Time on time.apple.com", self.check_apple_time),
                ("Check Apple Server Connection", self.check_apple_server_connection),
                ("Check ADE/DEP Enrollment", self.check_dep_enrollment),
                ("Check MDM Enrollment Status", lambda: self.run_sudo_command("sudo profiles status -type enrollment")),
                ("Check Installed Profiles", self.check_profiles_installed),
                ("Show Device Enrollment Log", self.show_device_enrollment_log),
                ("Remove Paired Bluetooth Devices", self.delete_bluetooth_devices)
            ]
            # Checkboxes for process steps with improved styling
            self.checkboxes = []
            for i, (text, _) in enumerate(self.buttons):
                checkbox = ctk.CTkCheckBox(
                    self.process_frame,
                    text=text,
                    state="disabled",
                    width=20,
                    height=20,
                    checkbox_width=16,
                    checkbox_height=16,
                    font=("SF Mono", 12),
                    border_width=1,
                    text_color="#FFFFFF",
                    text_color_disabled="#FFFFFF",
                    fg_color="#007AFF",
                    hover_color="#0056CC"
                )
                checkbox.grid(row=i+1, column=0, padx=8, pady=1, sticky="w")
                checkbox.configure(text_color="#FFFFFF")
                self.checkboxes.append(checkbox)
            logging.info("Creating Execute All Button")
            # Execute All button with improved styling
            self.execute_all_button = ctk.CTkButton(
                self.process_frame,
                text="Recheck All",
                command=self.auto_execute,
                width=100,
                height=22,
                anchor="center",
                font=("SF Pro", 12),
                fg_color="#007AFF",
                hover_color="#0056CC",
                corner_radius=4
            )
            self.execute_all_button.grid(row=len(self.buttons) + 1, column=0, padx=65, pady=(3, 3), sticky="w")
            logging.info("Creating System Information Frame")
            # System Information Frame with macOS-style border
            self.hardware_frame = ctk.CTkFrame(
                self.left_panel_frame,
                width=260,
                height=260,
                fg_color="#404040",
                border_width=1,
                border_color="#606060",
                corner_radius=8
            )
            self.hardware_frame.grid(row=1, column=0, padx=(0,0), pady=(0, 1), sticky="ew")
            self.hardware_frame.grid_propagate(False)
            self.hardware_frame.grid_columnconfigure(0, weight=1)
            self.hardware_frame.grid_columnconfigure(1, weight=2)
            logging.info("Creating System Information Header")
            # System information header
            self.hardware_label = ctk.CTkLabel(
                self.hardware_frame,
                text="System Information",
                font=("SF Pro Display", 16, "bold"),
                text_color="#73bee6"
            )
            self.hardware_label.grid(row=0, column=0, columnspan=2, padx=20, pady=1, sticky="nsew")
            # Initialize hardware_info here before it's used
            self.hardware_info = {
                "Model Identifier": None,
                "Chip": None,
                "Memory": None,
                "Storage Size": None,
                "Serial number": None,
                "macOS Version": None,
                "Activation Lock": None
            }
            logging.info("Creating System Information Labels")
            # System information labels with improved styling
            self.hardware_info_entries = {}
            for i, (key, _) in enumerate(self.hardware_info.items()):
                # Label for the key
                key_label = ctk.CTkLabel(
                    self.hardware_frame,
                    text=key + ":",
                    font=("SF Pro", 11),
                    text_color="#B3B3B3",
                    anchor="w"
                )
                key_label.grid(row=i+1, column=0, padx=(5, 5), pady=0, sticky="e")
                # Entry for the value (readonly, allows horizontal scrolling by cursor)
                value_entry = ctk.CTkEntry(
                    self.hardware_frame,
                    border_width=1,
                    font=("SF Pro", 11),
                    text_color="#FFFFFF",
                    width=160,
                    height=25,
                    corner_radius=4
                )
                value_entry.grid(row=i+1, column=1, padx=(0, 3), pady=0, sticky="w")
                value_entry.configure(state="readonly")
                self.hardware_info_entries[key] = value_entry
            # Create a frame for the save buttons with transparent background
            self.save_buttons_frame = ctk.CTkFrame(
                self.hardware_frame,
                fg_color="transparent"
            )
            self.save_buttons_frame.grid(row=len(self.hardware_info)+1, column=0, columnspan=2, pady=5, padx=5, sticky="ew")
            self.save_buttons_frame.grid_columnconfigure(0, weight=1)
            self.save_buttons_frame.grid_columnconfigure(1, weight=1)
            # Save to CSV button with improved styling
            self.save_csv_button = ctk.CTkButton(
                self.save_buttons_frame,
                text="Save to CSV",
                command=self.save_hardware_overview,
                font=("SF Pro", 11),
                fg_color="#007AFF",
                hover_color="#0056CC",
                text_color="white",
                corner_radius=4,
                width=90,
                height=22
            )
            self.save_csv_button.grid(row=0, column=0, pady=0, padx=(0, 1), sticky=" ")
            # Save to XML button with improved styling
            self.save_xml_button = ctk.CTkButton(
                self.save_buttons_frame,
                text="Save to XML",
                command=self.save_hardware_to_xml,
                font=("SF Pro", 11),
                fg_color="#007AFF",
                hover_color="#0056CC",
                text_color="white",
                corner_radius=4,
                width=90,
                height=22
            )
            self.save_xml_button.grid(row=0, column=1, pady=0, padx=(1, 0), sticky=" ")
            logging.info("Creating Shortcuts Frame")
            # Shortcuts Frame with macOS-style border - FIXED HEIGHT
            self.shortcut_frame = ctk.CTkFrame(
                self.left_panel_frame,
                width=260,
                height=225,
                fg_color="#404040",
                border_width=1,
                border_color="#606060",
                corner_radius=4
            )
            self.shortcut_frame.grid(row=2, column=0, padx=(0,0), pady=(0, 0), sticky="ew")
            self.shortcut_frame.grid_propagate(False)
            self.shortcut_frame.grid_columnconfigure(0, weight=1)
            logging.info("Creating Shortcuts Header")
            # Shortcuts header
            self.shortcuts_label = ctk.CTkLabel(
                self.shortcut_frame,
                text="Shortcuts",
                font=("SF Pro Display", 16, "bold"),
                text_color="#73bee6"
            )
            self.shortcuts_label.grid(row=0, column=0, padx=20, pady=1, sticky="nsew")
            logging.info("Creating Shortcuts Buttons")
            # Define macOS style button colors for shortcuts
            macos_button_color = "#555555"  # More visible button background
            macos_button_hover_color = "#666666"  # Lighter for hover state
            # Bluetooth button with improved styling
            self.open_bluetooth_button = ctk.CTkButton(
                self.shortcut_frame,
                text="Bluetooth",
                image=self.icons["bluetooth"],
                command=self.open_bluetooth_settings,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.open_bluetooth_button.grid(row=1, column=0, padx=10, pady=2, sticky="ew")
            # FindMy button with improved styling
            self.open_findmy_button = ctk.CTkButton(
                self.shortcut_frame,
                text="FindMy",
                image=self.icons["findmy"],
                command=self.open_findmy,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.open_findmy_button.grid(row=2, column=0, padx=10, pady=2, sticky="ew")
            # Profile button with improved styling
            self.open_profile_button = ctk.CTkButton(
                self.shortcut_frame,
                text="Device Management Profile",
                image=self.icons["profile"],
                command=self.open_system_preferences,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.open_profile_button.grid(row=3, column=0, padx=10, pady=2, sticky="ew")
            # System Information button with improved styling
            self.sys_info_button = ctk.CTkButton(
                self.shortcut_frame,
                text="System Information",
                image=self.icons["system"],
                command=self.show_system_info,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.sys_info_button.grid(row=4, column=0, padx=10, pady=2, sticky="ew")
            # Erase all content button with improved styling
            self.erase_button = ctk.CTkButton(
                self.shortcut_frame,
                text="Erase all content and settings..",
                image=self.icons["erase"],
                command=self.erase_app,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.erase_button.grid(row=5, column=0, padx=10, pady=2, sticky="ew")
            # Shutdown button with improved styling
            self.shutdown_button = ctk.CTkButton(
                self.shortcut_frame,
                text="Shutdown System",
                image=self.icons["shutdown"],
                command=self.shutdown_system,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.shutdown_button.grid(row=6, column=0, padx=10, pady=1, sticky="ew")
            # Right Frame (Process Logs) with macOS-style border
            self.right_frame = ctk.CTkFrame(
                self.main_frame,
                fg_color="#404040",
                border_width=1,
                border_color="#606060",
                corner_radius=4,
                height=490  # Set fixed height
            )
            self.right_frame.grid(row=0, column=1, padx=(0,0), pady=(3,3), sticky="nsew") # Occupies right column
            self.right_frame.grid_propagate(False)  # Prevent frame from resizing based on content
            self.right_frame.grid_columnconfigure(0, weight=1)  # Make width flexible
            self.right_frame.grid_rowconfigure(1, weight=1)  # Allow content to expand vertically within fixed height
            # Process logs header
            self.process_logs_label = ctk.CTkLabel(
                self.right_frame,
                text="Process Logs",
                font=("SF Pro Display", 16, "bold"),
                text_color="#73bee6"
            )
            self.process_logs_label.grid(row=0, column=0, padx=5, pady=(1, 0), sticky="nsew")
            # Process logs textbox with improved styling
            self.result_text = ctk.CTkTextbox(
                self.right_frame,
                wrap="word",
                fg_color="#1a1a1a",  # Dark but visible background
                border_width=1,
                border_color="#888888",  # Bright border
                corner_radius=4,
                font=("SF Mono", 12),
                text_color="#FFFFFF",  # Pure white text
                activate_scrollbars=False # Disable internal scrollbars to use external one
            )
            self.result_text.grid(row=1, column=0, padx=(3,3), pady=(0, 1), sticky="nsew")
            # Add a scrollbar to the result_text
            self.result_text_scrollbar = ctk.CTkScrollbar(
                self.right_frame,
                command=self.result_text.yview,
                orientation="vertical"
            )
            self.result_text_scrollbar.grid(row=1, column=1, sticky="ns", padx=(0,0), pady=(0,1))
            self.result_text.configure(yscrollcommand=self.result_text_scrollbar.set)
            
            # Configure text widget with initial content
            self.result_text.configure(state="normal")
            self.result_text.insert("1.0", "Process logs will appear here...\n")
            self.result_text.configure(state="disabled")
            self.result_text.see(ctk.END)

            # Initialize status bar after main content is ready (prevents small rectangle issue)
            # Defer status bar creation to ensure main content is fully loaded first
            self.after(50, self.setup_status_bar)

            # Bind window resize event (only for minsize enforcement, grid handles layout)
            self.bind("<Configure>", self.on_window_resize)

            self.result_queue = queue.Queue()
            self.after(100, self.check_result_queue)
            self.internet_connected = True
            # Defer datetime and internet status updates until status bar is created
            self.after(100, self.update_datetime)
            self.after(100, self.check_internet_periodically)
            self.update_hardware_info()
            # Force white text color for checkboxes after initialization
            self.after(100, self.force_checkbox_white_text)
            # Start the auto-execute process automatically
            self.after(1000, self.auto_execute)  # Start after 1 second to allow GUI to initialize
            # Start the blinking effect after status bar is ready
            self.blink_status = True
            self.after(150, self.blink_internet_status)

            # --- FORCE WINDOW TO FRONT AND FOCUS (fix blank UI on macOS) ---
            self.lift()
            self.focus_force()
            self.attributes('-topmost', True)
            self.after(500, lambda: self.attributes('-topmost', False))
        except Exception as e:
            error_msg = f"Error in InternetCheckerApp initialization: {str(e)}\n{traceback.format_exc()}"
            logging.error(error_msg)
            raise  # Re-raise the exception to be caught by the main error handler

    def setup_status_bar(self):
        """Setup status bar after main content is loaded to prevent layout issues"""
        try:
            # Check if status bar already exists (prevent double creation)
            if hasattr(self, 'status_frame') and self.status_frame.winfo_exists():
                return

            # Status bar frame with subtle border - created after main content
            self.status_frame = ctk.CTkFrame(
                self, # Parent is self, so it's always at the bottom of the window
                fg_color="#404040",
                border_width=1,
                border_color="#606060",
                corner_radius=4,
                height=35
            )
            # Placed at row 1 of self (bottom row), spanning the single main content column
            self.status_frame.grid(row=1, column=0, padx=5, pady=(1, 3), sticky="ew")

            # Configure status frame columns for left, center, right layout
            self.status_frame.grid_columnconfigure(0, weight=1) # Left section
            self.status_frame.grid_columnconfigure(1, weight=1) # Center section
            self.status_frame.grid_columnconfigure(2, weight=1) # Right section

            # Left section: Internet status with improved styling
            self.left_status_frame = ctk.CTkFrame(self.status_frame, fg_color="#404040")
            self.left_status_frame.grid(row=0, column=0, sticky="w", padx=(10, 5), pady=1)

            self.status_indicator = ctk.CTkCanvas(
                self.left_status_frame,
                width=16,
                height=16,
                bg="#404040",  # Match status bar background for seamless blend
                highlightthickness=0
            )
            self.status_indicator.pack(side="left", padx=(0, 8))
            self.status_light = self.status_indicator.create_oval(2, 2, 14, 14, fill="#34C759", outline="white", width=1)

            self.status_label = ctk.CTkLabel(
                self.left_status_frame,
                text="Internet connection",
                font=("SF Pro", 12),
                text_color="#B3B3B3"
            )
            self.status_label.pack(side="left")

            # Center section: Date and time with improved styling
            self.datetime_frame = ctk.CTkFrame(self.status_frame, fg_color="transparent")
            self.datetime_frame.grid(row=0, column=1, sticky="ns", pady=3)

            self.datetime_label = ctk.CTkLabel(
                self.datetime_frame,
                text="",
                font=("Menlo", 14),
                text_color="#d3de5c"
            )
            self.datetime_label.pack()

            # Right section: Exit button and "by vonzki" label with improved styling
            self.right_status_frame = ctk.CTkFrame(self.status_frame, fg_color="transparent")
            self.right_status_frame.grid(row=0, column=2, sticky="e", padx=5, pady=3)

            self.vonzk_label = ctk.CTkLabel(
                self.right_status_frame,
                text="@2025 vonzki",
                font=("SF Pro", 10),
                text_color="#686464"
            )
            self.vonzk_label.pack(side="left", padx=(0, 30))

            # Exit button in status bar (color confirmed from image)
            self.exit_button = ctk.CTkButton(
                self.right_status_frame,
                image=self.icons["exit"],
                text="Exit",
                command=self.on_closing,
                width=90,
                height=25,
                fg_color="#007AFF", # Assuming this was the blue accent color
                hover_color="#D7554E",
                corner_radius=4,
                font=("SF Pro", 13)
            )
            self.exit_button.pack(side="right")

        except Exception as e:
            logging.error(f"Error setting up status bar: {str(e)}")
            print(f"DEBUG: Error setting up status bar: {e}")
            # Create minimal status bar if there's an error
            try:
                self.status_frame = ctk.CTkFrame(self, fg_color="#2A2A2A", height=35)
                self.status_frame.grid(row=1, column=0, padx=5, pady=(1, 3), sticky="ew")
            except Exception as fallback_error:
                logging.error(f"Failed to create fallback status bar: {str(fallback_error)}")
                print(f"DEBUG: Failed to create fallback status bar: {fallback_error}")

    def force_window_refresh(self):
        """Force window refresh to eliminate rendering artifacts"""
        try:
            self.update()
            self.update_idletasks()
            # Force a redraw by briefly changing and restoring a property
            current_bg = self.cget("fg_color")
            self.configure(fg_color=current_bg)
            self.update()
        except Exception as e:
            print(f"DEBUG: Error in force_window_refresh: {e}")



    def show_left_panel_frames(self):
        """Legacy method - no longer needed since frames are always visible"""
        pass

    def validate_file_path(self, file_path):
        """Validate and sanitize file paths for security"""
        try:
            # Normalize the path to prevent directory traversal
            normalized_path = os.path.normpath(file_path)

            # Check for directory traversal attempts
            if '..' in normalized_path or normalized_path.startswith('/'):
                logging.warning(f"Potentially unsafe file path rejected: {file_path}")
                return None

            return normalized_path
        except Exception as e:
            logging.error(f"Error validating file path {file_path}: {str(e)}")
            return None

    def force_checkbox_white_text(self):
        """Force white text color for all checkboxes"""
        for checkbox in self.checkboxes:
            try:
                # Try different parameter names that might work
                checkbox.configure(text_color_disabled="#FFFFFF")
            except:
                try:
                    # Try accessing the internal text label directly
                    if hasattr(checkbox, '_text_label'):
                        checkbox._text_label.configure(fg="#FFFFFF")
                        checkbox._text_label.configure(text_color="#FFFFFF")
                except:
                    try:
                        # Try the tkinter approach
                        checkbox.configure(fg="#FFFFFF")
                    except Exception as e:
                        print(f"Could not set checkbox text color: {e}")
    def mount_network_share_finder_style(self, server, share_name, username, password):
        import subprocess, os, time, sys
        from tkinter import messagebox

        # Use helper to get SMB URL for connection
        if username and password:
            smb_url = f"smb://{username}:{password}@{server}/{share_name}"
        elif username:
            smb_url = f"smb://{username}@{server}/{share_name}"
        else:
            smb_url = f"smb://{server}/{share_name}"

        # First, check if share is already mounted
        try:
            for entry in os.listdir("/Volumes"):
                if entry.lower() == share_name.lower():
                    mount_point = os.path.join("/Volumes", entry)
                    if os.path.ismount(mount_point):
                        logging.info(f"Network share already mounted at {mount_point}")
                        return mount_point
        except Exception as e:
            logging.warning(f"Error checking existing mounts: {e}")

        # Check if running in PyInstaller bundle (different approach needed)
        is_pyinstaller = getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

        # Try AppleScript method first (but with different approach for PyInstaller)
        if not is_pyinstaller:
            applescript = f'''
            tell application "Finder"
                try
                    mount volume "{smb_url}"
                    return true
                on error
                    return false
                end try
            end tell
            '''
        else:
            # For PyInstaller, use a more direct AppleScript approach
            applescript = f'''
            try
                mount volume "{smb_url}"
                return true
            on error
                return false
            end try
            '''

        try:
            logging.info(f"Attempting to mount {smb_url} using AppleScript (PyInstaller: {is_pyinstaller})")
            result = subprocess.run(['osascript', '-e', applescript], capture_output=True, text=True, timeout=15)
            logging.info(f"AppleScript result: returncode={result.returncode}, stdout='{result.stdout}', stderr='{result.stderr}'")

            if result.returncode == 0 and "true" in result.stdout.lower():
                time.sleep(2)
                for entry in os.listdir("/Volumes"):
                    if entry.lower() == share_name.lower():
                        mount_point = os.path.join("/Volumes", entry)
                        if os.path.ismount(mount_point):
                            logging.info(f"Successfully mounted network share at {mount_point}")
                            return mount_point
                logging.warning("AppleScript succeeded but mount point not found")
            else:
                logging.warning(f"AppleScript failed: {result.stderr}")
        except Exception as e:
            logging.error(f"AppleScript execution failed: {e}")

        # Fallback: Try direct mount command (works better in .app bundles)
        try:
            logging.info(f"Trying fallback mount method for {smb_url}")
            # Create mount point if it doesn't exist
            mount_point = f"/Volumes/{share_name}"

            # For PyInstaller, try a different approach first
            if is_pyinstaller:
                try:
                    # Try using open command which works well in .app bundles
                    open_cmd = f"open '{smb_url}'"
                    result = subprocess.run(open_cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        time.sleep(3)  # Give it time to mount
                        for entry in os.listdir("/Volumes"):
                            if entry.lower() == share_name.lower():
                                found_mount_point = os.path.join("/Volumes", entry)
                                if os.path.ismount(found_mount_point):
                                    logging.info(f"Successfully mounted using 'open' command at {found_mount_point}")
                                    return found_mount_point
                except Exception as e:
                    logging.warning(f"'open' command failed: {e}")

            # Traditional mount_smbfs approach
            if not os.path.exists(mount_point):
                try:
                    os.makedirs(mount_point, exist_ok=True)
                except Exception as e:
                    logging.warning(f"Could not create mount point {mount_point}: {e}")
                    # Try without creating the directory
                    pass

            # Try mount_smbfs command
            if username and password:
                mount_cmd = f"mount_smbfs '//{username}:{password}@{server}/{share_name}' '{mount_point}'"
            elif username:
                mount_cmd = f"mount_smbfs '//{username}@{server}/{share_name}' '{mount_point}'"
            else:
                mount_cmd = f"mount_smbfs '//{server}/{share_name}' '{mount_point}'"

            result = subprocess.run(mount_cmd, shell=True, capture_output=True, text=True, timeout=15)
            if result.returncode == 0:
                time.sleep(1)  # Give it a moment
                if os.path.exists(mount_point) and os.path.ismount(mount_point):
                    logging.info(f"Successfully mounted using mount_smbfs at {mount_point}")
                    return mount_point
            else:
                logging.warning(f"mount_smbfs failed: {result.stderr}")
        except Exception as e:
            logging.error(f"Fallback mount method failed: {e}")

        # If all methods failed
        logging.error("All mount methods failed")
        messagebox.showerror("Mount Failed", "Could not mount the network share. Please connect manually in Finder.")
        return None
    def mount_network_share_at_startup(self):
        """Mount the network share at application startup (Finder style)"""
        server_address = self.network_config["server"]
        share_name = self.network_config["share"]
        username = self.network_config["username"]
        password = self.network_config["password"]
        mount_point = self.mount_network_share_finder_style(server_address, share_name, username, password)
        if mount_point:
            self.network_config["mount_point"] = mount_point
            print(f"Network share mounted at {mount_point}")
            return True
        else:
            print("Could not mount network share.")
            return False
    def load_icon(self, path, size):
        """Load an icon from the specified path and resize it to the given size"""
        try:
            if os.path.exists(path):
                # Open the image file
                img = Image.open(path)
                # Create a CTkImage with the same image for both light and dark modes
                return ctk.CTkImage(light_image=img, dark_image=img, size=size)
            else:
                logging.warning(f"Icon not found: {path}")
                return None
        except (IOError, OSError) as e:
            logging.error(f"Error loading icon {path}: {str(e)}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error loading icon {path}: {str(e)}")
            return None
    def update_datetime(self):
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.configure(text=current_time)
        self.after(1000, self.update_datetime)  # Update every second
    def check_internet_connection(self):
        try:
            # Attempt to connect to a reliable server (Google's DNS)
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            return True
        except (OSError, socket.timeout, socket.gaierror) as e:
            logging.debug(f"Internet connection check failed: {str(e)}")
            return False
        except Exception as e:
            logging.warning(f"Unexpected error in internet connection check: {str(e)}")
            return False
    def check_internet_periodically(self):
        try:
            if self.winfo_exists() and hasattr(self, 'status_indicator'):
                self.internet_connected = self.check_internet_connection()
                if self.internet_connected:
                    self.status_indicator.itemconfig(self.status_light, fill="#34C759")
                    self.status_label.configure(text="Internet connection active")
                else:
                    self.status_indicator.itemconfig(self.status_light, fill="#FF3B30")
                    self.status_label.configure(text="No internet connection")
                self.after(5000, self.check_internet_periodically)  # Check every 5 seconds
        except Exception as e:
            logging.debug(f"Error checking internet periodically: {str(e)}")
    def blink_internet_status(self):
        try:
            if self.winfo_exists() and hasattr(self, 'status_indicator') and self.blink_status:
                current_color = self.status_indicator.itemcget(self.status_light, "fill")
                bg_color = "#404040"
                active_color = "#34C759" if self.internet_connected else "#FF3B30"
                new_color = bg_color if current_color != bg_color else active_color
                self.status_indicator.itemconfig(self.status_light, fill=new_color)
                self.after(500, self.blink_internet_status)  # Blink every 500ms
        except Exception as e:
            logging.debug(f"Error blinking internet status: {str(e)}")
    def check_internet(self):
        if not self.internet_connected:
            return "No internet connection."
        try:
            subprocess.check_output(["ping", "-c", "1", "-W", "3", "8.8.8.8"], timeout=5)
            return "Internet connection is active."
        except subprocess.CalledProcessError:
            return "No internet connection."
        except subprocess.TimeoutExpired:
            return "Internet connection check timed out."
        except Exception as e:
            logging.error(f"Unexpected error in internet check: {str(e)}")
            return f"Internet check error: {str(e)}"
    def check_apple_time(self):
        """Check time with Apple's time server"""
        if not self.internet_connected:
            return "No internet connection. Cannot check Apple time server."
        try:
            # Run sntp command to get time from Apple's time server - we don't need the output
            subprocess.check_output("sntp time.apple.com", shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)
            # Parse the output to get the current time
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            return f"Current time: {current_time}"
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
            return f"Error getting time from Apple server: {str(e)}"
    def check_apple_server_connection(self):
        """Check connection to Apple servers using network utility"""
        if not self.internet_connected:
            return "No internet connection. Cannot check Apple server connection."
        # Apple server IPs and domains to check
        apple_servers = [
            "*************",  # Apple's main server
            "************",   # Apple's iCloud server
            "swscan.apple.com",
            "www.apple.com"
        ]
        results = []
        # Check for established connections to Apple servers
        for server in apple_servers:
            try:
                # Try to establish a connection
                if "." in server:  # It's an IP address or domain
                    try:
                        socket.create_connection((server, 443), timeout=3)
                        results.append(f"Connection to Apple server {server} - Success")
                    except:
                        results.append(f"Connection to Apple server {server} - Failed")
                else:
                    results.append(f"Skipping invalid server address: {server}")
            except Exception as e:
                results.append(f"Error checking {server}: {str(e)}")
        return "\n".join(results)
    def run_sudo_command(self, command, timeout=10):
        global SUDO_PASSWORD
        logging.info(f"Running sudo command: {command}")
        # Try running the command with the current password
        full_command = f"echo {SUDO_PASSWORD} | sudo -S {command}"
        result = self.run_command(full_command, timeout=timeout)
        # Remove 'Password:' prompt if present
        if result and result.strip().startswith("Password:"):
            result = result.replace("Password:", "").strip()
        # Check if the password was incorrect
        if ("is not in the sudoers file" in result or "incorrect password" in result or "Sorry, try again." in result):
            from tkinter import messagebox
            messagebox.showwarning(
                "Sudo Password Required",
                "Default sudo authentication failed.\nPlease enter your sudo password."
            )
            password_result = get_sudo_password(
                self,
                title="Sudo Password Required",
                message="Authentication failed with default password.\nPlease enter your sudo password:"
            )
            if password_result["cancelled"]:
                return "Operation cancelled by user."
            SUDO_PASSWORD = password_result["password"]
            full_command = f"echo {SUDO_PASSWORD} | sudo -S {command}"
            result = self.run_command(full_command, timeout=timeout)
            if result and result.strip().startswith("Password:"):
                result = result.replace("Password:", "").strip()
        return result
    def run_command(self, command, timeout=10):
        try:
            logging.info(f"Running command: {command}")
            output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.STDOUT, timeout=timeout)
            # Remove 'Password:' prompt if present in output
            lines = output.splitlines()
            filtered_lines = [line for line in lines if not line.strip().startswith("Password:")]
            return "\n".join(filtered_lines).strip()
        except subprocess.CalledProcessError as e:
            logging.error(f"Command failed: {command}\nOutput: {e.output.strip() if e.output else 'No output'}")
            # Remove 'Password:' prompt if present in error output
            lines = e.output.splitlines() if e.output else []
            filtered_lines = [line for line in lines if not line.strip().startswith("Password:")]
            return f"Error: {' '.join(filtered_lines).strip()}"
        except subprocess.TimeoutExpired:
            logging.error(f"Command timed out: {command}")
            return "Error: Command timed out"
        except Exception as e:
            logging.error(f"Unexpected error running command: {command}\nError: {str(e)}")
            return f"Error: Unexpected error - {str(e)}"
    def show_device_enrollment_log(self):
        """Show device enrollment log - CRITICAL: Fast search for company messages"""
        print("DEBUG: Starting CRITICAL device enrollment log check (fast)...")

        company_lines = []
        # Single fast search for company messages
        try:
            print("DEBUG: Fast search for company configuration messages...")
            # Use a single, fast command with short timeout
            cmd = 'log show --info --last 6h --style compact | grep -i "can automatically configure" | head -n 5'
            result = self.run_command(cmd, timeout=10)

            if result and result.strip():
                lines = result.split('\n')
                import re
                seen_companies = set()
                for line in lines:
                    line = line.strip()
                    match = re.search(r"([\w .,&'-]+) can automatically configure your Mac", line)
                    if match:
                        company = match.group(1).strip()
                        if company not in seen_companies:
                            company_lines.append(line)
                            seen_companies.add(company)
        except Exception as e:
            print(f"DEBUG: Fast search failed: {e}")

        # If no messages found, try one trigger attempt
        try:
            if not company_lines:
                print("DEBUG: No messages found, trying one trigger...")
                # Quick trigger and search
                self.run_sudo_command("sudo profiles renew -type enrollment")
                # Wait briefly and search recent logs
                time.sleep(1)
                cmd = 'log show --info --last 2m --style compact | grep -i "can automatically configure" | head -n 1'
                result = self.run_command(cmd, timeout=8)
                if result and result.strip():
                    lines = result.split('\n')
                    import re
                    seen_companies = set([re.search(r"([\w .,&'-]+) can automatically configure your Mac", l).group(1).strip() for l in company_lines if re.search(r"([\w .,&'-]+) can automatically configure your Mac", l)])
                    for line in lines:
                        line = line.strip()
                        match = re.search(r"([\w .,&'-]+) can automatically configure your Mac", line)
                        if match:
                            company = match.group(1).strip()
                            if company not in seen_companies:
                                company_lines.append(line)
                                seen_companies.add(company)
        except Exception as e:
            print(f"DEBUG: Trigger search failed: {e}")

        # If still no results found
        if not company_lines:
            print("DEBUG: No device enrollment log entries found")
            return "No recent Device Enrollment log entries found."
        return "\n".join(company_lines)






    def open_system_preferences(self):
        os.system("open /System/Library/PreferencePanes/Profiles.prefPane")
        return "Opened relevant System Settings sections. Please check the Profiles pane."
    def exit_app(self):
        self.quit()
    def shutdown_system(self):
        """Show shutdown confirmation dialog"""
        command = "osascript -e 'tell app \"System Events\" to display dialog \"Are you sure you want to shut down the system?\" buttons {\"Cancel\", \"Shut Down\"} default button \"Cancel\" with icon caution' -e 'if button returned of result is \"Shut Down\" then tell app \"System Events\" to shut down'"
        self.run_sudo_command(command)
        return "Shutdown dialog displayed"
    def auto_execute(self):
        # Pause and show a message if not connected to the internet
        import tkinter.messagebox
        if not self.internet_connected:
            self.display_result("\n[INFO] No internet connection detected.\n\nPlease connect to the internet to start Security Lock Checks.\n")
            tkinter.messagebox.showinfo("Internet Required", "No internet connection detected.\n\nPlease connect to the internet to start Security Lock Checks.")
            # Wait until internet is available
            while not self.check_internet_connection():
                self.update()
                time.sleep(1)
            self.display_result("\n[INFO] Internet connection detected. Continuing Security Lock Checks...\n")
        # Reset all checkboxes to unchecked state
        for checkbox in self.checkboxes:
            self.after(0, checkbox.deselect) # Use self.after to run in main thread
        # Clear the process logs textbox
        self.after(0, lambda: self.result_text.configure(state="normal"))
        self.after(0, lambda: self.result_text.delete("1.0", ctk.END))
        self.after(0, lambda: self.result_text.configure(state="disabled"))
        # Reset any previous highlighting
        self.current_highlighted_checkbox_idx = -1
        threading.Thread(target=self._auto_execute_thread, daemon=True).start()
    def _auto_execute_thread(self):
        for i, (text, command) in enumerate(self.buttons):
            # Un-highlight previous step and highlight current step
            if self.current_highlighted_checkbox_idx != -1:
                self.after(0, lambda idx=self.current_highlighted_checkbox_idx: self.checkboxes[idx].configure(text_color="#FFFFFF"))
            self.current_highlighted_checkbox_idx = i
            self.after(0, lambda idx=i: self.checkboxes[idx].configure(text_color="#FFD700")) # Highlight current step in gold
            try:
                if not self.internet_connected and text not in ["Check Internet", "Open Profiles"]:
                    result = "Skipped due to no internet connection"
                elif callable(command):
                    try:
                        result = command()
                    except Exception as e:
                        logging.error(f"Error executing {text}: {str(e)}\n{traceback.format_exc()}")
                        result = f"Error executing {text}: {str(e)}"
                else:
                    result = self.run_command(command)
                if result is not None:
                    self.result_queue.put(f"{text}:\n{result}")
                self.after(0, lambda idx=i: self.checkboxes[idx].select())
            except Exception as e:
                logging.error(f"Error in _auto_execute_thread for {text}: {str(e)}\n{traceback.format_exc()}")
                self.result_queue.put(f"Error executing {text}: {str(e)}")
            finally:
                # Ensure un-highlighting and checking happens even if there's an error
                self.after(0, lambda idx=i: self.checkboxes[idx].configure(text_color="#FFFFFF")) # Revert color after completion
            time.sleep(1)  # Add a small delay between steps
        # After all steps are done, clear any remaining highlight
        if self.current_highlighted_checkbox_idx != -1:
            self.after(0, lambda idx=self.current_highlighted_checkbox_idx: self.checkboxes[idx].configure(text_color="#FFFFFF"))
            self.current_highlighted_checkbox_idx = -1
    def display_result(self, result):
        self.result_text.configure(state="normal")
        # Define tags for different types of messages using macOS colors
        self.result_text.tag_config("success", foreground="#34C759")
        self.result_text.tag_config("error", foreground="#FF3B30")
        self.result_text.tag_config("info", foreground="#ee6565")
        self.result_text.tag_config("result", foreground="#007AFF")
        self.result_text.tag_config("dep_enrolled", foreground="#FF3B30")  # RED
        self.result_text.tag_config("dep_not_enrolled", foreground="#007AFF")  # BLUE
        self.result_text.tag_config("bt_not_connected", foreground="#007AFF")  # BLUE for Not Connected
        self.result_text.tag_config("ade_company", foreground="#FFD700")  # Gold highlight for ADE/DEP company
        self.result_text.tag_config("gold_title", foreground="#79d3ea")  # Gold for check titles
        self.result_text.tag_config("not_found", foreground="#8E8E93")  # Gray for not found/profile not found
        self.result_text.tag_config("light_red", foreground="#FF6B6B")  # Light red for Bluetooth bullet points
        self.result_text.tag_config("gold_note", foreground="#FFD700")  # Gold for Note messages
        # Split the result into lines
        lines = result.split('\n')
        print(f"DEBUG: display_result lines = {lines}")  # Debug print
        # Filter out sudo password prompts from logs
        lines = [line for line in lines if not line.strip().startswith("Password:")]
        # List of security lock check titles to highlight
        gold_titles = [
            "Check Time on time.apple.com:",
            "Check Apple Server Connection:",
            "Check ADE/DEP Enrollment:",
            "Raw status output:",
            "Check MDM Enrollment Status:",
            "Check Installed Profiles:",
            "Show Device Enrollment Log:",
            "Current Bluetooth devices:",
            "Remove Paired Bluetooth Devices:"
        ]
        # First line is usually the command/check name
        if lines and ":" in lines[0]:
            command_parts = lines[0].split(":", 1)
            full_title = command_parts[0] + ":"
            if full_title in gold_titles:
                self.result_text.insert(ctk.END, full_title + "\n", "gold_title")
            else:
                self.result_text.insert(ctk.END, full_title + "\n")
            if len(lines) > 1:
                # Define check type flags before the loop to avoid NameError
                is_dep_enrollment_check = "Check ADE/DEP Enrollment:" in lines[0]
                is_mdm_enrollment_check = "Check MDM Enrollment Status:" in lines[0]
                is_bt_devices_check = "Current Bluetooth devices:" in lines[0] or "Remove Paired Bluetooth Devices:" in lines[0]
                in_raw_status = False
                for line in lines[1:]:
                    import re
                    # 0. Display 'Raw status output:' line in gold and set in_raw_status
                    if is_dep_enrollment_check and line.strip().startswith("Raw status output:"):
                        in_raw_status = True
                        # Add a blank line before 'Raw status output:' for better readability
                        self.result_text.insert(ctk.END, "\n")
                        self.result_text.insert(ctk.END, line + "\n", "gold_title")
                        continue
                    # 1. NOTE: lines (yellow, with gold phrase if present)
                    if is_dep_enrollment_check and line.strip().startswith("NOTE:"):
                        match = re.search(r"([\w .,&'-]+) can automatically configure your Mac", line)
                        if match:
                            start, end = match.span()
                            self.result_text.insert(ctk.END, line[:start], "info")
                            self.result_text.insert(ctk.END, match.group(1) + " can automatically configure your Mac", "ade_company")
                            self.result_text.insert(ctk.END, line[end:] + "\n", "info")
                        else:
                            self.result_text.insert(ctk.END, line + "\n", "info")
                        continue
                    # 2. ENROLLED/INSTALLED/NOT ENROLLED/NOT INSTALLED (red/blue)
                    if is_dep_enrollment_check and ("Enrollment Status:" in line or "DEP Profile:" in line):
                        match = re.search(r'(ENROLLED|NOT ENROLLED|INSTALLED|NOT INSTALLED)', line)
                        if match:
                            start, end = match.span()
                            tag = "dep_enrolled" if match.group() in ["ENROLLED", "INSTALLED"] and "NOT" not in match.group() else "dep_not_enrolled"
                            self.result_text.insert(ctk.END, line[:start])
                            self.result_text.insert(ctk.END, match.group(), tag)
                            self.result_text.insert(ctk.END, line[end:] + "\n")
                        else:
                            self.result_text.insert(ctk.END, line + "\n")
                        continue
                    # 3. Raw status output: only Yes/No is colored (red/blue), rest is normal
                    if is_dep_enrollment_check and in_raw_status and (":" in line):
                        match = re.search(r'(:\s*)(Yes|No)\b', line)
                        if match:
                            prefix = line[:match.start(2)]
                            value = match.group(2)
                            suffix = line[match.end(2):]
                            tag = "dep_enrolled" if value == "Yes" else "dep_not_enrolled"
                            self.result_text.insert(ctk.END, prefix)
                            self.result_text.insert(ctk.END, value, tag)
                            self.result_text.insert(ctk.END, suffix + "\n")
                        else:
                            self.result_text.insert(ctk.END, line + "\n")
                        continue
                    # --- Bluetooth: highlight 'No bluetooth devices found.' in gray ---
                    if is_bt_devices_check and "No bluetooth devices found." in line:
                        self.result_text.insert(ctk.END, line + "\n", "not_found")
                        continue
                    # --- Restore original process log highlighting logic for all other logs ---
                    # Skip raw status output line as it's already handled above
                    if is_dep_enrollment_check and (line.strip().startswith("Raw status output:")):
                        continue
                    elif is_dep_enrollment_check and (":" in line):
                        match = re.search(r'\b(Yes|No)\b', line)
                        if match:
                            start, end = match.span()
                            tag = "dep_enrolled" if match.group() == "Yes" else "dep_not_enrolled"
                            self.result_text.insert(ctk.END, line[:start])
                            self.result_text.insert(ctk.END, match.group(), tag)
                            self.result_text.insert(ctk.END, line[end:] + "\n")
                        else:
                            self.result_text.insert(ctk.END, line + "\n")
                        continue
                    elif is_mdm_enrollment_check and (":" in line):
                        match = re.search(r'\b(Yes|No)\b', line)
                        if match:
                            start, end = match.span()
                            tag = "dep_enrolled" if match.group() == "Yes" else "dep_not_enrolled"
                            self.result_text.insert(ctk.END, line[:start])
                            self.result_text.insert(ctk.END, match.group(), tag)
                            self.result_text.insert(ctk.END, line[end:] + "\n")
                        else:
                            self.result_text.insert(ctk.END, line + "\n")
                        continue
                    elif is_bt_devices_check and "Not Connected" in line:
                        idx = line.lower().find("not connected")
                        if idx != -1:
                            self.result_text.insert(ctk.END, line[:idx])
                            self.result_text.insert(ctk.END, line[idx:idx+13], "bt_not_connected")
                            self.result_text.insert(ctk.END, line[idx+13:] + "\n")
                            continue
                    if "Success" in line:
                        idx = line.find("Success")
                        if idx != -1:
                            self.result_text.insert(ctk.END, line[:idx])
                            self.result_text.insert(ctk.END, "Success", "success")
                            self.result_text.insert(ctk.END, line[idx+7:] + "\n")
                        else:
                            self.result_text.insert(ctk.END, line + "\n")
                        continue
                    if ("Error" in line or "Failed" in line or "No internet connection" in line):
                        self.result_text.insert(ctk.END, line + "\n", "error")
                        continue
                    # Consolidated conditions for result highlighting (removed duplicates)
                    if (line.startswith("DEP Status:") or
                          line.strip() in ["No", "None"] or
                          any(phrase in line for phrase in [
                              "No profiles installed", "No profiles found",
                              "Enrolled via DEP:", "MDM enrollment:"
                          ]) or
                          ("Check Time on time.apple.com:" in lines[0] and line.startswith("Current time"))):
                        self.result_text.insert(ctk.END, line + "\n", "result")
                        continue
                    if "No Device Enrollment configuration was found for this computer" in line:
                        parts = line.split("No Device Enrollment configuration was found for this computer")
                        self.result_text.insert(ctk.END, parts[0])
                        self.result_text.insert(ctk.END, "No Device Enrollment configuration was found for this computer", "result")
                        if len(parts) > 1:
                            self.result_text.insert(ctk.END, parts[1] + "\n")
                        else:
                            self.result_text.insert(ctk.END, "\n")
                        continue
                    if "Please" in line or "ensure" in line or "If you've recently" in line:
                        self.result_text.insert(ctk.END, line + "\n", "info")
                        continue
                    # (keep the original ADE/DEP Enrollment highlighting for other checks)
                    if "Show Device Enrollment Log:" in lines[0]:
                        match = re.search(r"([\w .,&'-]+ can automatically configure your Mac\.)", line)
                        if match:
                            start, end = match.span()
                            self.result_text.insert(ctk.END, line[:start])
                            self.result_text.insert(ctk.END, match.group(1), "ade_company")
                            self.result_text.insert(ctk.END, line[end:] + "\n")
                            continue
                        # Highlight PROFILE_NOT_FOUND and similar
                        not_found_patterns = [
                            r"No Device Enrollment for this device: PROFILE_NOT_FOUND",
                            r"PROFILE_NOT_FOUND",
                            r"No Device Enrollment configuration was found for this computer"
                        ]
                        for pat in not_found_patterns:
                            nf_match = re.search(pat, line)
                            if nf_match:
                                start, end = nf_match.span()
                                self.result_text.insert(ctk.END, line[:start])
                                self.result_text.insert(ctk.END, nf_match.group(), "not_found")
                                self.result_text.insert(ctk.END, line[end:] + "\n")
                                break
                        else:
                            # Highlight Error/Failed in red
                            if re.search(r"Error|Failed", line):
                                self.result_text.insert(ctk.END, line + "\n", "error")
                                continue
                            # Highlight Success in green
                            if "Success" in line:
                                idx = line.find("Success")
                                self.result_text.insert(ctk.END, line[:idx])
                                self.result_text.insert(ctk.END, "Success", "success")
                                self.result_text.insert(ctk.END, line[idx+7:] + "\n")
                                continue
                            # Highlight MDM/DEP/enrollment in blue ONLY if not already matched above
                            if re.search(r"MDM|DEP|enrollment", line, re.IGNORECASE):
                                self.result_text.insert(ctk.END, line + "\n", "result")
                                continue
                            # Default: normal text
                            self.result_text.insert(ctk.END, line + "\n")
                        continue
                    # --- Bluetooth: show bullet points in light red (Success highlighting handled by general rule) ---
                    if is_bt_devices_check and line.strip().startswith("・") and "Success" not in line:
                        self.result_text.insert(ctk.END, line.strip() + "\n", "light_red")
                        continue
                    # --- Bluetooth: show gold Note messages ---
                    if is_bt_devices_check and ("Note:" in line and ("modern versions" in line or "manually remove" in line)):
                        self.result_text.insert(ctk.END, line.strip() + "\n", "gold_note")
                        continue
                    # --- Bluetooth: show light Disclaimer messages ---
                    if is_bt_devices_check and "DISCLAIMER:" in line:
                        self.result_text.insert(ctk.END, line.strip() + "\n", "light_red")
                        continue
                    # --- Bluetooth: show connected and not connected device lists ---
                    if is_bt_devices_check and (
                        line.startswith("Connected Bluetooth devices") or
                        line.startswith("Not Connected Bluetooth devices")
                    ):
                        self.result_text.insert(ctk.END, line + "\n", "result")
                        continue
                    # Only treat as device name if not a bullet point or Note message
                    if is_bt_devices_check and line and not line.endswith(":") and not line.strip().startswith("・") and "Note:" not in line:
                        self.result_text.insert(ctk.END, line + "\n")
                        continue
        else:
            for line in lines:
                self.result_text.insert(ctk.END, line + "\n")
        self.result_text.insert(ctk.END, "\n")
        self.result_text.configure(state="disabled")
        self.result_text.see(ctk.END)
        self.update_idletasks()  # Force GUI update
    def check_result_queue(self):
        try:
            while True:
                result = self.result_queue.get_nowait()
                # Process the result immediately in the main thread
                self.display_result(result)
        except queue.Empty:
            pass
        finally:
            self.after(100, self.check_result_queue)
    def check_activation_lock_status(self):
        command = "system_profiler SPHardwareDataType | grep 'Activation Lock Status'"
        try:
            output = subprocess.check_output(command, shell=True, text=True)
            return f"Activation Lock Status:\n{output.strip()}"
        except subprocess.CalledProcessError:
            return "Failed to check Activation Lock Status"
    def update_hardware_info(self):
        info = self.get_hardware_info()
        # Map 'Activation Lock Status' to 'Activation Lock' for display
        if 'Activation Lock' in self.hardware_info_entries and 'Activation Lock Status' in info:
            info['Activation Lock'] = info['Activation Lock Status']
        for key, entry in self.hardware_info_entries.items():
            value = info.get(key, "")
            entry.configure(state="normal")
            entry.delete(0, ctk.END)
            entry.insert(0, value)
            # Highlight Activation Lock: green if Disabled, red if Enabled, gray otherwise
            if key == "Activation Lock":
                if value.strip().lower() == "enabled":
                    entry.configure(text_color="#db5462")  # Red
                elif value.strip().lower() == "disabled":
                    entry.configure(text_color="#34C759")  # Green
                else:
                    entry.configure(text_color="#B3B3B3")  # Gray
                entry.configure(font=("SF Pro", 12, "bold"))
            else:
                entry.configure(text_color="#FFFFFF", font=("SF Pro", 11))
            entry.configure(state="readonly")
        # Force update of the GUI
        self.update_idletasks()
    def get_processor_info(self):
        """Get detailed processor information for both Intel and Apple Silicon"""
        try:
            # Get the chip brand string
            chip_brand = subprocess.check_output("sysctl -n machdep.cpu.brand_string", shell=True, text=True).strip()

            # Determine architecture
            arch = platform.machine()

            if arch == "arm64":
                # Apple Silicon
                vendor = "Apple Inc."
                # Clean up the chip name for Apple Silicon
                if "Apple" in chip_brand:
                    model = chip_brand
                else:
                    model = f"Apple {chip_brand}"
            else:
                # Intel
                vendor = "Intel Corporation"
                model = chip_brand

            return {
                "vendor": vendor,
                "model": model,
                "architecture": arch
            }
        except Exception as e:
            logging.error(f"Error getting processor info: {e}")
            # Fallback based on architecture
            arch = platform.machine()
            if arch == "arm64":
                return {"vendor": "Apple Inc.", "model": "Apple Silicon", "architecture": arch}
            else:
                return {"vendor": "Intel Corporation", "model": "Intel Processor", "architecture": arch}

    def get_storage_size(self):
        """Get storage size and convert to common marketing sizes (128GB, 256GB, 512GB, etc.)"""
        def convert_to_marketing_size(size_gb):
            """Convert actual disk size to common marketing sizes"""
            # Common marketing sizes in GB
            marketing_sizes = [128, 256, 512, 1000, 2000, 4000, 8000]

            # Find the closest marketing size
            for marketing_size in marketing_sizes:
                # If actual size is within 15% of marketing size, use marketing size
                if abs(size_gb - marketing_size) / marketing_size <= 0.15:
                    if marketing_size >= 1000:
                        return f"{marketing_size // 1000} TB"
                    else:
                        return f"{marketing_size} GB"

            # If no close match, round to nearest common size
            if size_gb < 200:
                return "128 GB"
            elif size_gb < 400:
                return "256 GB"
            elif size_gb < 750:
                return "512 GB"
            elif size_gb < 1500:
                return "1 TB"
            elif size_gb < 3000:
                return "2 TB"
            elif size_gb < 6000:
                return "4 TB"
            else:
                return "8 TB"

        try:
            # Use the original method that was working (from Intel version)
            output = subprocess.check_output(
                "diskutil list | grep 'internal, physical' | awk '{print $1}' | xargs -I {} diskutil list {} | grep 'GUID_partition_scheme' | grep '\\*.*[GT]B' | sed 's/.*\\*\\([0-9.]*\\) \\([GT]B\\).*/\\1 \\2/' | paste -sd ', ' -",
                shell=True, text=True
            ).strip()

            if output and ("GB" in output or "TB" in output):
                # Parse the size and convert to marketing size
                try:
                    if "TB" in output:
                        size_gb = float(output.replace("TB", "").strip()) * 1000
                    else:
                        size_gb = float(output.replace("GB", "").strip())

                    return convert_to_marketing_size(size_gb)
                except:
                    return output  # Return original if parsing fails
        except:
            pass

        # Fallback: try system_profiler but convert to marketing size
        try:
            output = subprocess.check_output(
                "system_profiler SPStorageDataType | grep 'Capacity' | head -1 | awk -F': ' '{print $2}' | sed 's/^ *//' | sed 's/ *$//' | sed 's/ (.*//'",
                shell=True, text=True
            ).strip()

            if output and ("GB" in output or "TB" in output):
                try:
                    if "TB" in output:
                        size_gb = float(output.replace("TB", "").strip()) * 1000
                    else:
                        size_gb = float(output.replace("GB", "").strip())

                    return convert_to_marketing_size(size_gb)
                except:
                    return output
        except:
            pass

        return "512 GB"  # Default fallback to common size

    def get_hardware_info(self):
        commands = {
            "Model Identifier": "sysctl -n hw.model",
            "Chip": "sysctl -n machdep.cpu.brand_string",
            "Memory": "sysctl -n hw.memsize | awk '{ printf \"%.0f GB\", $0/1024/1024/1024 }'",
            "Serial number": "ioreg -c IOPlatformExpertDevice -d 2 | grep -i 'IOPlatformSerialNumber' | awk -F \"\\\"\" '{print $4}'",
            "macOS Version": "sw_vers -productVersion"
        }
        info = {}
        for key, command in commands.items():
            try:
                output = subprocess.check_output(command, shell=True, text=True).strip()
                info[key] = output.strip('"')
            except subprocess.CalledProcessError:
                info[key] = "Unable to retrieve"

        # Add storage size using the improved method
        info["Storage Size"] = self.get_storage_size()
        # Activation Lock logic for Apple Silicon and Intel Macs with T2
        try:
            # Check for Apple Silicon or T2 chip
            arch = platform.machine()
            if arch == "arm64":
                # Apple Silicon Macs support Activation Lock
                try:
                    output = subprocess.check_output(
                        "system_profiler SPHardwareDataType | grep -i 'Activation Lock' | awk -F ': ' '{print $2}'",
                        shell=True, text=True
                    ).strip()
                    info["Activation Lock Status"] = output if output else "Supported"
                except subprocess.CalledProcessError:
                    info["Activation Lock Status"] = "Supported"
            else:
                # Intel Mac - check for T2 chip
                t2_check = subprocess.check_output(
                    "system_profiler SPiBridgeDataType 2>/dev/null | grep 'Model Name'",
                    shell=True, text=True
                ).strip()
                if not t2_check or "T2" not in t2_check:
                    info["Activation Lock Status"] = "Not supported"
                else:
                    # Try to get Activation Lock status
                    output = subprocess.check_output(
                        "system_profiler SPHardwareDataType | grep -i 'Activation Lock' | awk -F ': ' '{print $2}'",
                        shell=True, text=True
                    ).strip()
                    info["Activation Lock Status"] = output if output else "Unknown"
        except Exception:
            # Default to supported for Apple Silicon, not supported for Intel without T2
            arch = platform.machine()
            if arch == "arm64":
                info["Activation Lock Status"] = "Supported"
            else:
                info["Activation Lock Status"] = "Not supported"
        return info

    def get_disk_serial_number(self):
        """Get the internal disk serial number using ioreg command"""
        try:
            # Use ioreg to get internal disk serial number - filter for internal disks only
            cmd = 'ioreg -l | grep -A10 "Physical Interconnect Location.*Internal" | grep "Serial Number" | head -1 | awk -F\'"\' \'{print $4}\''
            output = subprocess.check_output(cmd, shell=True, text=True).strip()
            if output and len(output) > 5:
                logging.info(f"Retrieved internal disk serial number: {output}")
                return output
            else:
                logging.warning("Could not retrieve internal disk serial number")
                return "Unknown"
        except Exception as e:
            logging.error(f"Error getting internal disk serial number: {e}")
            return "Unknown"

    def get_disk_models_for_reports(self):
        """Get internal disk models for CSV and XML reports (supports multiple internal disks)"""
        try:
            # Try system_profiler first (works on most Macs) - filter for internal disks only
            cmd = "system_profiler SPSerialATADataType SPNVMeDataType 2>/dev/null | grep -B5 'Detachable Drive: No' | grep 'Model:' | awk -F':' '{gsub(/^[ \\t]+|[ \\t]+$/, \"\", $2); print $2}' | paste -sd ', ' -"
            output = subprocess.check_output(cmd, shell=True, text=True).strip()
            if output and output != "Unknown":
                logging.info(f"Retrieved internal disk models: {output}")
                return output

            # Fallback: try diskutil for older Macs - only internal disks
            cmd = "diskutil list | grep 'internal, physical' | awk '{print $1}' | xargs -I {} diskutil info {} | grep 'Device / Media Name' | awk -F':' '{gsub(/^[ \\t]+|[ \\t]+$/, \"\", $2); print $2}' | paste -sd ', ' -"
            output = subprocess.check_output(cmd, shell=True, text=True).strip()
            if output and output != "Unknown":
                logging.info(f"Retrieved internal disk models (fallback): {output}")
                return output

            return "Unknown"
        except Exception as e:
            logging.error(f"Error getting internal disk models: {e}")
            return "Unknown"

    def get_internal_disk_info(self):
        """Get information about the internal Apple disk specifically"""
        try:
            # Get detailed disk information from system_profiler
            output = subprocess.check_output("system_profiler SPSerialATADataType", shell=True, text=True)

            # Look for Apple SSD with Bay Name (indicates internal)
            lines = output.split('\n')
            current_disk = {}
            internal_disk = None

            for line in lines:
                line = line.strip()
                if line.endswith(':') and ('APPLE' in line or 'SSD' in line):
                    # Start of a new disk section
                    current_disk = {'name': line.rstrip(':')}
                elif 'Model:' in line and current_disk:
                    current_disk['model'] = line.split(':', 1)[1].strip()
                elif 'BSD Name:' in line and current_disk:
                    current_disk['bsd_name'] = line.split(':', 1)[1].strip()
                elif 'Bay Name:' in line and current_disk:
                    current_disk['bay_name'] = line.split(':', 1)[1].strip()
                    # If it has a Bay Name, it's likely internal
                    if 'APPLE' in current_disk.get('model', '').upper():
                        internal_disk = current_disk
                        break

            if internal_disk:
                return {
                    'model': internal_disk.get('model', 'Unknown'),
                    'bsd_name': internal_disk.get('bsd_name', '/dev/disk0'),
                    'is_internal': True
                }
            else:
                # Fallback: look for any Apple SSD
                for line in lines:
                    if 'Model:' in line and 'APPLE' in line.upper():
                        model = line.split(':', 1)[1].strip()
                        return {
                            'model': model,
                            'bsd_name': '/dev/disk1',  # Common for internal Apple SSD
                            'is_internal': True
                        }

                # Final fallback: use first disk
                return {
                    'model': 'Unknown Internal Disk',
                    'bsd_name': '/dev/disk0',
                    'is_internal': False
                }

        except Exception as e:
            logging.error(f"Error getting internal disk info: {e}")
            return {
                'model': 'Unknown Internal Disk',
                'bsd_name': '/dev/disk0',
                'is_internal': False
            }
    def check_dep_enrollment(self):
        """Check DEP/ADE enrollment status - simplified and fast"""
        import re
        try:
            # Step 1: Quick status check
            status_result = self.run_sudo_command("sudo profiles status -type enrollment")
            if not status_result or "Error" in status_result:
                return "Error: Unable to retrieve enrollment status."

            # Step 2: Parse status
            dep_status = "Unknown"
            mdm_status = "Unknown"

            for line in status_result.splitlines():
                line = line.strip()
                if "Enrolled via DEP:" in line:
                    dep_status = line.split(":", 1)[1].strip()
                elif "MDM enrollment:" in line:
                    mdm_status = line.split(":", 1)[1].strip()

            # Step 3: Check for cloud config file
            cloud_config_path = "/var/db/ConfigurationProfiles/Settings/.cloudConfigProfileInstalled"
            cloud_config_installed = os.path.exists(cloud_config_path)

            # Step 4: Check device enrollment log for company eligibility
            log_result = self.show_device_enrollment_log()
            ade_company = None

            if log_result:
                # Look for company configuration message
                company_match = re.search(r"([\w\s.,&'()-]+?)\s+can automatically configure your Mac", log_result)
                if company_match:
                    ade_company = company_match.group(1).strip()

            # Step 5: Build summary
            summary = []
            summary.append(f"DEP (ADE) Enrollment Status: {'ENROLLED' if dep_status == 'Yes' else 'NOT ENROLLED'}")
            summary.append(f"MDM Enrollment Status: {'ENROLLED' if mdm_status == 'Yes' else 'NOT ENROLLED'}")

            if cloud_config_installed:
                summary.append("DEP Profile: INSTALLED (.cloudConfigProfileInstalled present)")
            else:
                summary.append("DEP Profile: NOT INSTALLED (.cloudConfigProfileInstalled missing)")

            # Add note if eligible but not enrolled
            if ade_company and dep_status != 'Yes':
                summary.append(f"NOTE: Device is eligible for ADE/DEP (per logs: '{ade_company} can automatically configure your Mac'), but not currently enrolled.")

            summary.append("")
            summary.append("Raw status output:")
            summary.append(status_result.strip())

            return "\n".join(summary)

        except Exception as e:
            return f"Error checking DEP enrollment: {str(e)}"
    def open_findmy(self):
        """Open FindMy application"""
        try:
            # Try different possible names for the FindMy app
            for app_name in ["FindMy", "Find My", "FindMy.app", "Find My Mac"]:
                result = os.system(f"open -a '{app_name}'")
                if result == 0:  # Command succeeded
                    return f"Opened {app_name} application."
            # If none of the above worked, try opening it via URL scheme
            os.system("open x-apple.findmy://")
            return "Attempted to open FindMy application via URL scheme."
        except Exception as e:
            return f"Error opening FindMy: {str(e)}"
    def save_hardware_overview(self):
        """Save hardware information to CSV file"""
        try:
            import os  # Ensure os is imported for local save fallback
            logging.info("Starting CSV save operation")
            info = self.get_hardware_info()
            logging.info(f"Hardware info retrieved: {info}")

            mount_point = self.network_config["mount_point"]
            # Use helper for SMB URL (for connection) and display string (for user)
            share_display = self.get_share_display_string()

            def mount_network_share():
                try:
                    server_address = self.network_config["server"]
                    share_name = self.network_config["share"]
                    username = self.network_config["username"]
                    password = self.network_config["password"]
                    mount_point = self.mount_network_share_finder_style(server_address, share_name, username, password)
                    if mount_point:
                        self.network_config["mount_point"] = mount_point
                        return True
                    else:
                        return False
                except Exception as e:
                    logging.error(f"Error mounting network share: {e}")
                    return False

            mount_successful = mount_network_share()
            if mount_successful and os.path.exists(mount_point) and os.path.isdir(mount_point):
                initialdir = mount_point
                self.display_result(f"Using network share {share_display} for saving files.")
            else:
                from tkinter import messagebox
                answer = messagebox.askyesno("Network Share Not Available", "Network share not available. Do you want to save locally instead?")
                if answer:
                    import os
                    initialdir = os.path.expanduser("~")
                    self.display_result("Saving to local folder.")
                else:
                    self.display_result("Save cancelled.")
                    return

            current_date = datetime.now().strftime("%Y%m%d")
            serial = info.get("Serial number") or "UnknownSerial"
            serial = serial.replace(" ", "_")
            suggested_filename = f"{serial}_{current_date}.csv"

            logging.info(f"Opening file dialog with initialdir: {initialdir}")
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv")],
                initialdir=initialdir,
                initialfile=suggested_filename
            )

            if file_path:
                logging.info(f"Saving CSV to: {file_path}")
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(["Property", "Value"])
                    for key, value in info.items():
                        writer.writerow([key, value])
                    # Add disk information for reports
                    disk_serial = self.get_disk_serial_number()
                    if disk_serial != "Unknown":
                        writer.writerow(["Disk Serial Number", disk_serial])
                    disk_models = self.get_disk_models_for_reports()
                    if disk_models != "Unknown":
                        writer.writerow(["Disk Models", disk_models])
                self.display_result(f"System Information saved to {file_path}")
                logging.info("CSV save completed successfully")
            else:
                self.display_result("Save operation cancelled")
                logging.info("CSV save cancelled by user")

        except Exception as e:
            error_msg = f"Error saving CSV file: {str(e)}"
            logging.error(error_msg)
            self.display_result(error_msg)
            from tkinter import messagebox
            messagebox.showerror("Save Error", error_msg)
    def create_xml_info_dialog(self):
        """Create a dialog to collect additional information for XML export"""
        # Create a new dialog window
        dialog = ctk.CTkToplevel(self)
        dialog.title("XML Custom Field Information")
        dialog.geometry("400x330")  # Initial size
        dialog.resizable(False,False)
        dialog.grab_set()  # Make the dialog modal
        # Center the dialog over the main window
        self.update_idletasks()  # Ensure main window geometry is up to date
        main_x = self.winfo_rootx()
        main_y = self.winfo_rooty()
        main_w = self.winfo_width()
        main_h = self.winfo_height()
        dialog_w = 400
        dialog_h = 330
        pos_x = main_x + (main_w - dialog_w) // 2
        pos_y = main_y + (main_h - dialog_h) // 2
        dialog.geometry(f"{dialog_w}x{dialog_h}+{pos_x}+{pos_y}")
        # Set dark theme
        dialog.configure(fg_color="#1E1E1E")
        # Create form fields
        ctk.CTkLabel(dialog, text="Please enter the following information:", font=("SF Pro", 14, "bold"), text_color="#FFFFFF").pack(pady=(5, 0))
        # Add note about required fields
        # Create a frame for the required note to hold multiple elements
        required_note_frame = ctk.CTkFrame(dialog, fg_color="#1E1E1E")
        required_note_frame.pack(pady=(0, 5))
        # Add the text in parts to allow different colors
        ctk.CTkLabel(required_note_frame, text="Fields marked with ", font=("SF Pro", 12), text_color="#B3B3B3").pack(side="left")
        ctk.CTkLabel(required_note_frame, text="*", font=("SF Pro", 12, "bold"), text_color="#CD5C5C").pack(side="left")
        ctk.CTkLabel(required_note_frame, text=" are required", font=("SF Pro", 12), text_color="#B3B3B3").pack(side="left")
        # Create a frame for the form fields - adjust the bottom padding
        form_frame = ctk.CTkFrame(dialog, fg_color="#1E1E1E")
        form_frame.pack(fill="both", expand=False, padx=20, pady=(2, 0))  # Changed expand to False and bottom padding to 0
        # Configure columns for the form frame
        form_frame.grid_columnconfigure(0, weight=0)  # Label column
        form_frame.grid_columnconfigure(1, weight=0)  # Asterisk column
        form_frame.grid_columnconfigure(2, weight=1)  # Entry column
        form_frame.grid_columnconfigure(3, weight=0)  # Error message column
        # Create validation message labels (initially empty)
        tp_validation = ctk.CTkLabel(form_frame, text="", text_color="#CD5C5C", font=("SF Pro", 11))
        load_validation = ctk.CTkLabel(form_frame, text="", text_color="#CD5C5C", font=("SF Pro", 11))
        usertag_validation = ctk.CTkLabel(form_frame, text="", text_color="#CD5C5C", font=("SF Pro", 11))
        # TP Number field with required asterisk
        ctk.CTkLabel(form_frame, text="TP Number:", anchor="w", text_color="#FFFFFF").grid(row=0, column=0, padx=(10, 2), pady=5, sticky="w")
        ctk.CTkLabel(form_frame, text="*", text_color="#CD5C5C", font=("SF Pro", 14, "bold")).grid(row=0, column=1, padx=(0, 5), pady=5, sticky="w")
        tp_number_var = ctk.StringVar()
        tp_entry = ctk.CTkEntry(form_frame, textvariable=tp_number_var, width=200, text_color="#FFFFFF")
        tp_entry.grid(row=0, column=2, padx=5, pady=5, sticky="w")
        tp_validation.grid(row=0, column=3, padx=5, pady=5, sticky="w")
        # Load Number field with required asterisk
        ctk.CTkLabel(form_frame, text="Load number:", anchor="w", text_color="#FFFFFF").grid(row=1, column=0, padx=(10, 2), pady=5, sticky="w")
        ctk.CTkLabel(form_frame, text="*", text_color="#CD5C5C", font=("SF Pro", 14, "bold")).grid(row=1, column=1, padx=(0, 5), pady=5, sticky="w")
        load_number_var = ctk.StringVar()
        load_entry = ctk.CTkEntry(form_frame, textvariable=load_number_var, width=200, text_color="#FFFFFF")
        load_entry.grid(row=1, column=2, padx=5, pady=5, sticky="w")
        load_validation.grid(row=1, column=3, padx=5, pady=5, sticky="w")
        # Usertag field with required asterisk
        ctk.CTkLabel(form_frame, text="Usertag:", anchor="w", text_color="#FFFFFF").grid(row=2, column=0, padx=(10, 2), pady=5, sticky="w")
        ctk.CTkLabel(form_frame, text="*", text_color="#CD5C5C", font=("SF Pro", 14, "bold")).grid(row=2, column=1, padx=(0, 5), pady=5, sticky="w")
        current_user = os.getlogin()
        default_usertag = "" if current_user == "root" else current_user
        usertag_var = ctk.StringVar(value=default_usertag)
        usertag_entry = ctk.CTkEntry(form_frame, textvariable=usertag_var, width=200, text_color="#FFFFFF")
        usertag_entry.grid(row=2, column=2, padx=5, pady=5, sticky="w")
        usertag_validation.grid(row=2, column=3, padx=5, pady=5, sticky="w")
        # Client Asset Number field (not required)
        ctk.CTkLabel(form_frame, text="Client Asset Number:", anchor="w", text_color="#FFFFFF").grid(row=3, column=0, padx=(10, 2), pady=5, sticky="w")
        ctk.CTkLabel(form_frame, text="", font=("SF Pro", 14), text_color="#FFFFFF").grid(row=3, column=1, padx=(0, 5), pady=5, sticky="w")
        asset_number_var = ctk.StringVar()
        asset_entry = ctk.CTkEntry(form_frame, textvariable=asset_number_var, width=200, text_color="#FFFFFF")
        asset_entry.grid(row=3, column=2, padx=5, pady=5, sticky="w")
        # Comment field (not required)
        ctk.CTkLabel(form_frame, text="Comment:", anchor="w", text_color="#FFFFFF").grid(row=4, column=0, padx=(10, 2), pady=5, sticky="w")
        ctk.CTkLabel(form_frame, text="", font=("SF Pro", 14), text_color="#FFFFFF").grid(row=4, column=1, padx=(0, 5), pady=10, sticky="w")
        comment_var = ctk.StringVar()
        comment_entry = ctk.CTkEntry(form_frame, textvariable=comment_var, width=200, text_color="#FFFFFF")
        comment_entry.grid(row=4, column=2, padx=5, pady=10, sticky="w")
        # Create a frame for buttons with minimal padding
        button_frame = ctk.CTkFrame(dialog, fg_color="#1E1E1E")
        button_frame.pack(fill="x", padx=20, pady=(10, 5))  # Top padding set to 0
        # Dictionary to store the results
        result = {"confirmed": False, "data": {}}
        # Function to clear all validation messages
        def clear_validation_messages():
            tp_validation.configure(text="")
            load_validation.configure(text="")
            usertag_validation.configure(text="")
        # Function to handle OK button click
        def on_ok():
            # Clear any previous validation messages
            clear_validation_messages()
            # Check if required fields are filled
            tp_number = tp_number_var.get().strip()
            load_number = load_number_var.get().strip()
            usertag = usertag_var.get().strip()
            # Flag to track if validation passed
            validation_passed = True
            if not tp_number:
                tp_validation.configure(text="Required")
                tp_entry.focus_set()
                validation_passed = False
            if not load_number:
                load_validation.configure(text="Required")
                if validation_passed:  # Only set focus if no previous field had an error
                    load_entry.focus_set()
                validation_passed = False
            if not usertag:
                usertag_validation.configure(text="Required")
                if validation_passed:  # Only set focus if no previous field had an error
                    usertag_entry.focus_set()
                validation_passed = False
            # If validation failed, return without closing the dialog
            if not validation_passed:
                return
            # All validation passed, save the data and close the dialog
            result["confirmed"] = True
            result["data"] = {
                "tp_number": tp_number,
                "load_number": load_number,
                "usertag": usertag,
                "asset_number": asset_number_var.get().strip(),
                "comment": comment_var.get().strip()
            }
            dialog.destroy()
        # Function to handle Cancel button click
        def on_cancel():
            dialog.destroy()
        # Add OK and Cancel buttons
        ctk.CTkButton(button_frame, text="OK", command=on_ok,
                     fg_color="#007ACC", hover_color="#005999").pack(side="right", padx=20)
        ctk.CTkButton(button_frame, text="Cancel", command=on_cancel,
                     fg_color="#333333", hover_color="#444444").pack(side="right", padx=5)
        # Set focus to the first field
        tp_entry.focus_set()
        # Wait for the dialog to be closed
        self.wait_window(dialog)
        return result
    def save_hardware_to_xml(self):
        """Save hardware information to XML file compatible with Blancco Management Console"""
        try:
            import os  # Ensure os is imported for local save fallback
            logging.info("Starting XML save operation")

            dialog_result = self.create_xml_info_dialog()
            if not dialog_result["confirmed"]:
                self.display_result("XML export cancelled")
                return

            form_data = dialog_result["data"]
            tp_number = form_data["tp_number"]
            load_number = form_data["load_number"]
            usertag = form_data["usertag"]
            asset_number = form_data["asset_number"]
            comment = form_data["comment"]
            document_id = f"{tp_number}@{usertag}"

            info = self.get_hardware_info()
            logging.info(f"Hardware info for XML: {info}")

            mount_point = self.network_config["mount_point"]
            share_display = self.get_share_display_string()

            def mount_network_share():
                try:
                    server_address = self.network_config["server"]
                    share_name = self.network_config["share"]
                    username = self.network_config["username"]
                    password = self.network_config["password"]
                    mount_point = self.mount_network_share_finder_style(server_address, share_name, username, password)
                    if mount_point:
                        self.network_config["mount_point"] = mount_point
                        return True
                    else:
                        return False
                except Exception as e:
                    logging.error(f"Error mounting network share for XML: {e}")
                    return False

            mount_successful = mount_network_share()
            if mount_successful and os.path.exists(mount_point) and os.path.isdir(mount_point):
                initialdir = mount_point
                self.display_result(f"Using network share {share_display} for saving files.")
            else:
                from tkinter import messagebox
                answer = messagebox.askyesno("Network Share Not Available", "Network share not available. Do you want to save locally instead?")
                if answer:
                    import os
                    initialdir = os.path.expanduser("~")
                    self.display_result("Saving to local folder.")
                else:
                    self.display_result("Save cancelled.")
                    return

            suggested_filename = f"{tp_number}.xml" if tp_number else (f"{load_number}.xml" if load_number else "export.xml")

            logging.info(f"Opening XML file dialog with initialdir: {initialdir}")
            file_path = filedialog.asksaveasfilename(
                defaultextension=".xml",
                filetypes=[("XML files", "*.xml")],
                initialfile=suggested_filename,
                initialdir=initialdir
            )

            if not file_path:
                self.display_result("Save operation cancelled")
                logging.info("XML save cancelled by user")
                return
            # Create XML structure based on blancco_report.xml format (working format)
            root = ET.Element("root")
            report = ET.SubElement(root, "report")
            # Create blancco_data section
            blancco_data = ET.SubElement(report, "blancco_data")
            # Create description section
            description = ET.SubElement(blancco_data, "description")
            ET.SubElement(description, "document_id").text = document_id
            # Create document_log section
            document_log = ET.SubElement(description, "document_log")
            # Add log entry
            log_entry = ET.SubElement(document_log, "log_entry")
            author = ET.SubElement(log_entry, "author")
            product_name = ET.SubElement(author, "product_name", id="51", name="VS Mac Apple Silicon Checker")
            product_name.text = "Blancco Management Console"
            ET.SubElement(author, "product_version").text = "2.2.9"
            ET.SubElement(author, "product_revision").text = "N/A"
            # Add current date/time in ISO format with timezone
            current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S+0900")
            ET.SubElement(log_entry, "date").text = current_time
            ET.SubElement(log_entry, "integrity").text = "Not applicable, user created"
            ET.SubElement(log_entry, "key")
            # Add description entries
            description_entries = ET.SubElement(description, "entry", name="description_entries")
            ET.SubElement(description_entries, "entry", name="verified", type="string").text = "false"
            ET.SubElement(description_entries, "entry", name="verified", type="string").text = "false"
            ET.SubElement(description_entries, "entry", name="verified", type="string").text = "true"
            # Create hardware report section
            hardware_report = ET.SubElement(blancco_data, "blancco_hardware_report")
            system_entries = ET.SubElement(hardware_report, "entries", name="system")
            # Add manufacturer entry
            ET.SubElement(system_entries, "entry", name="manufacturer", type="string").text = "Apple Inc."
            # Map our hardware info keys to the XML format keys
            key_mapping = {
                "Model Identifier": "model",
                "Serial number": "serial"
            }
            # Add hardware info entries
            for key, value in info.items():
                if key in key_mapping:
                    ET.SubElement(system_entries, "entry", name=key_mapping[key], type="string").text = value
            # Add chassis type
            ET.SubElement(system_entries, "entry", name="chassis_type", type="string").text = "Notebook"
            # Add RAM information (NEW) - using correct format from old_blancco_report.xml
            memory_str = info.get("Memory", "8GB")
            ET.SubElement(system_entries, "entry", name="ram", type="string").text = memory_str
            # Add Processor information (Intel and Apple Silicon)
            processor_entries = ET.SubElement(hardware_report, "entries", name="processors")
            processor_device = ET.SubElement(processor_entries, "entries", name="processor")
            processor_info = self.get_processor_info()
            ET.SubElement(processor_device, "entry", name="vendor", type="string").text = processor_info["vendor"]
            ET.SubElement(processor_device, "entry", name="model", type="string").text = processor_info["model"]
            # Get actual core count (physical cores)
            cores = "4"
            try:
                core_count = subprocess.check_output("sysctl -n hw.physicalcpu", shell=True, text=True).strip()
                if core_count and core_count.isdigit():
                    cores = core_count
            except:
                pass
            # Get actual frequency (different methods for Intel vs Apple Silicon)
            speed = "2800"  # Default fallback
            try:
                arch = platform.machine()
                if arch == "arm64":
                    # Apple Silicon - try to get frequency from system_profiler
                    try:
                        freq_output = subprocess.check_output(
                            "system_profiler SPHardwareDataType | grep 'Processor Speed' | awk -F': ' '{print $2}' | sed 's/ GHz//' | sed 's/,//'",
                            shell=True, text=True
                        ).strip()
                        if freq_output:
                            # Convert GHz to MHz
                            freq_ghz = float(freq_output)
                            speed = str(int(freq_ghz * 1000))
                    except:
                        # Fallback for Apple Silicon based on chip type
                        chip_info = processor_info["model"]
                        if "M1" in chip_info:
                            speed = "3200"  # M1 typical frequency
                        elif "M2" in chip_info:
                            speed = "3500"  # M2 typical frequency
                        elif "M3" in chip_info:
                            speed = "4000"  # M3 typical frequency
                        else:
                            speed = "3000"  # Generic Apple Silicon fallback
                else:
                    # Intel - use traditional method
                    freq = subprocess.check_output("sysctl -n hw.cpufrequency", shell=True, text=True).strip()
                    if freq and freq.isdigit():
                        speed = str(int(int(freq) / 1000000))
            except:
                pass
            ET.SubElement(processor_device, "entry", name="cores", type="uint").text = cores
            ET.SubElement(processor_device, "entry", name="nominal_speed", type="uint").text = speed
            # Add disk information
            disks_entries = ET.SubElement(hardware_report, "entries", name="disks")
            disk_entry = ET.SubElement(disks_entries, "entries", name="disk")
            ET.SubElement(disk_entry, "entry", name="id", type="uint").text = "34"
            ET.SubElement(disk_entry, "entry", name="index", type="uint").text = "1"
            # Use disk models for XML (supports multiple disks)
            disk_models = self.get_disk_models_for_reports()
            # For XML, use the first disk model or fallback to internal disk info
            if disk_models != "Unknown" and disk_models:
                disk_model = disk_models.split(',')[0].strip()
            else:
                internal_disk = self.get_internal_disk_info()
                disk_model = internal_disk['model']

            ET.SubElement(disk_entry, "entry", name="model", type="string").text = disk_model
            # Determine vendor from disk model
            vendor = "Unknown"
            if "SAMSUNG" in disk_model.upper():
                vendor = "Samsung"
            elif "APPLE" in disk_model.upper():
                vendor = "Apple"
            elif "SANDISK" in disk_model.upper():
                vendor = "SanDisk"
            elif "INTEL" in disk_model.upper():
                vendor = "Intel"
            elif "CRUCIAL" in disk_model.upper():
                vendor = "Crucial"
            elif "WD" in disk_model.upper() or "WESTERN" in disk_model.upper():
                vendor = "Western Digital"
            else:
                vendor = "Unknown"
            ET.SubElement(disk_entry, "entry", name="vendor", type="string").text = vendor
            # Use disk serial if available, otherwise system serial
            disk_serial = self.get_disk_serial_number()
            if disk_serial == "Unknown":
                # Fallback to system serial number
                disk_serial = info.get("Serial number", "Unknown")
            ET.SubElement(disk_entry, "entry", name="serial", type="string").text = disk_serial
            # Interface type for Intel Macs
            interface_type = "SATA"
            try:
                disk_info = subprocess.check_output("diskutil info / | grep 'Protocol'", shell=True, text=True).strip()
                if "NVMe" in disk_info:
                    interface_type = "NVMe"
                elif "SATA" in disk_info:
                    interface_type = "SATA"
                elif "PCI" in disk_info:
                    interface_type = "PCI"
            except:
                pass
            ET.SubElement(disk_entry, "entry", name="interface_type", type="string").text = interface_type
            # Get storage capacity
            storage_str = info.get("Storage Size", "512GB")
            # Convert storage to bytes (approximate)
            try:
                if "TB" in storage_str:
                    capacity = int(float(storage_str.replace("TB", "").strip()) * 1000000000000)
                else:
                    # Extract numeric part from storage string (e.g., "512 GB" -> "512")
                    numeric_part = storage_str.replace("GB", "").strip()
                    if numeric_part:
                        capacity = int(float(numeric_part) * 1000000000)
                    else:
                        capacity = 512000000000  # Default 512GB
            except (ValueError, TypeError):
                capacity = 512000000000  # Default 512GB if conversion fails
            ET.SubElement(disk_entry, "entry", name="capacity", type="uint").text = str(capacity)
            # Create user data section (original working format)
            user_data = ET.SubElement(report, "user_data")
            fields = ET.SubElement(user_data, "entries", name="fields")
            ET.SubElement(fields, "entry", name="Load_number", type="string").text = load_number
            ET.SubElement(fields, "entry", name="TP Number", type="string").text = tp_number
            ET.SubElement(fields, "entry", name="Usertag", type="string").text = usertag if usertag else ""
            ET.SubElement(fields, "entry", name="Client_Asset_number", type="string").text = asset_number
            ET.SubElement(fields, "entry", name="Comment", type="string").text = comment
            # Convert to XML string with proper formatting
            xml_str = ET.tostring(root, encoding='utf-8')
            # Parse and prettify the XML
            dom = xml.dom.minidom.parseString(xml_str)
            pretty_xml = dom.toprettyxml(indent="  ")
            # Remove empty lines and fix formatting
            lines = [line for line in pretty_xml.split('\n') if line.strip()]
            formatted_xml = '\n'.join(lines)
            # Write to file
            logging.info(f"Writing XML to file: {file_path}")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(formatted_xml)
            self.display_result(f"System Information saved to XML file: {file_path}")
            logging.info("XML save completed successfully")

        except Exception as e:
            error_msg = f"Error saving XML file: {str(e)}"
            logging.error(error_msg)
            self.display_result(error_msg)
            from tkinter import messagebox
            messagebox.showerror("Save Error", error_msg)
    def open_bluetooth_settings(self):
        os.system("open /System/Library/PreferencePanes/Bluetooth.prefPane")
    def show_system_info(self):
        os.system("open -a 'System Information'")
    def erase_app(self):
        """Open the Erase Assistant app"""
        # The path to Erase Assistant varies by macOS version
        if MACOS_VERSION >= (11, 0, 0):  # Big Sur or later
            command = "open /System/Library/CoreServices/Erase\\ Assistant.app"
        elif MACOS_VERSION >= (10, 15, 0):  # Catalina
            command = "open -a 'System Preferences' /System/Library/PreferencePanes/Reset.prefPane"
        else:
            return "Erase Assistant is not available on this version of macOS."
        result = self.run_command(command)
        return f"Opened Erase Assistant: {result}"
    def delete_bluetooth_devices(self):
        """Delete all paired Bluetooth devices using the Terminal-based method by default."""
        # First, check and display paired devices
        devices_info = self.check_bluetooth_devices()
        print(f"DEBUG: Bluetooth devices_info = {devices_info}")
        self.display_result(f"Current Bluetooth devices:\n{devices_info}")

        # Always run in Terminal (recommended/Automator style)
        self.run_bluetooth_removal_in_terminal()
        # Only show the explicit process message, never log None
        self.display_result(
            "Remove Paired Bluetooth Devices:\n"
            "・Bluetooth removal script is running in a new Terminal window. Please follow any prompts and close the window when done.\n"
            "Note: On modern versions of macOS, paired or registered Bluetooth devices may not be fully removed by script. If devices remain, remove manually in System Settings > Bluetooth\n"
            "⚠️ DISCLAIMER:\n"
            "This app does not guarantee that all results will be accurate, as multiple factors can affect the verification of security locks on Mac computers. Its purpose is to assist in checking security lock status."
        )
        # Do not call self.display_result with None or any result that could be None
        return

    def check_findmy_status(self):
        """Check if FindMy is enabled on the device"""
        try:
            # First check if FindMy is enabled via iCloud
            command = "defaults read ~/Library/Preferences/MobileMeAccounts.plist Accounts | grep -A 5 -B 5 'FindMyMac'"
            output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)
            if "FindMyMac = 1" in output:
                return "FindMy is ENABLED on this device."
            elif "FindMyMac = 0" in output:
                return "FindMy is DISABLED on this device."
            else:
                # If the first check doesn't give clear results, try an alternate method
                command2 = "defaults read /Library/Preferences/com.apple.FindMyMac.plist FMMEnabled"
                output2 = subprocess.check_output(command2, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)
                if "1" in output2:
                    return "FindMy is ENABLED on this device (alternate check)."
                else:
                    return "FindMy is DISABLED on this device (alternate check)."
        except subprocess.CalledProcessError:
            # If both methods fail, try a third method
            try:
                command3 = "sudo profiles show | grep -i 'find my'"
                output3 = self.run_sudo_command(command3)
                if "Find My Mac" in output3:
                    return "FindMy appears to be configured on this device."
                else:
                    return "FindMy status could not be determined with certainty. It may be DISABLED."
            except:
                return "Failed to check FindMy status."
        except subprocess.TimeoutExpired:
            return "FindMy status check timed out."
    def check_bluetooth_devices(self):
        """Check for paired Bluetooth devices (robust parsing for different macOS versions)"""
        print("DEBUG: Entered check_bluetooth_devices")
        try:
            command = "system_profiler SPBluetoothDataType"
            output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)
            print(f"DEBUG: Bluetooth system_profiler output = {output[:500]}...")

            lines = output.split('\n')
            devices_connected = []
            devices_not_connected = []
            all_devices = []  # Fallback for older macOS versions
            current_section = None

            # First pass: try modern format with sections
            for line in lines:
                stripped = line.strip()
                indent = len(line) - len(line.lstrip(' '))
                print(f"DEBUG: Section={current_section}, Indent={indent}, Line='{stripped}'")

                # Detect section headers (flexible patterns for different macOS versions)
                if stripped.lower() == "connected:":
                    current_section = "connected"
                    print(f"DEBUG: Entered Connected section")
                    continue
                elif stripped.lower() == "not connected:":
                    current_section = "not_connected"
                    print(f"DEBUG: Entered Not Connected section")
                    continue
                elif any(pattern in stripped.lower() for pattern in ["connected devices", "paired devices"]):
                    current_section = "connected"
                    print(f"DEBUG: Entered Connected section (alt pattern)")
                    continue
                elif any(pattern in stripped.lower() for pattern in ["not connected devices", "unpaired devices"]):
                    current_section = "not_connected"
                    print(f"DEBUG: Entered Not Connected section (alt pattern)")
                    continue

                # Look for device names (ending with ':' and properly indented)
                if indent >= 4 and stripped.endswith(":") and len(stripped) > 1:
                    device_name = stripped.rstrip(":")

                    # Skip system entries, technical details, and service names
                    skip_patterns = [
                        "bluetooth controller", "controller", "adapter", "hardware", "features",
                        "settings", "devices", "services", "bluetooth file", "incoming", "outgoing",
                        "serial ports", "internet sharing", "file transfer", "file exchange",
                        "paired", "configured", "etc"
                    ]

                    # Skip MAC addresses (pattern: xx-xx-xx-xx-xx-xx or similar)
                    if (any(skip in device_name.lower() for skip in skip_patterns) or
                        len(device_name.replace("-", "").replace(":", "")) == 12 and
                        all(c in "0123456789abcdefABCDEF-:" for c in device_name)):
                        print(f"DEBUG: Skipping system/technical entry '{device_name}'")
                        continue

                    # Only accept device names that look like real device names
                    # Real device names typically contain letters and are longer than 3 chars
                    if (len(device_name) > 3 and
                        any(c.isalpha() for c in device_name) and
                        not device_name.lower().startswith("bluetooth") and
                        not all(c in "0123456789abcdefABCDEF-:" for c in device_name)):

                        print(f"DEBUG: Found valid device '{device_name}' in section {current_section}")

                        # Add to appropriate section
                        if current_section == "connected":
                            devices_connected.append(device_name)
                        elif current_section == "not_connected":
                            devices_not_connected.append(device_name)
                        else:
                            # No section detected yet, add to general list for fallback
                            all_devices.append(device_name)
                    else:
                        print(f"DEBUG: Rejected invalid device name '{device_name}'")

            # Fallback for older macOS versions (Catalina and earlier)
            if not devices_connected and not devices_not_connected and not all_devices:
                print("DEBUG: No devices found with modern parsing, trying legacy format...")

                # Try alternative parsing for older formats (macOS Catalina and earlier)
                for line in lines:
                    stripped = line.strip()
                    indent = len(line) - len(line.lstrip(' '))

                    # Pattern 1: Device names at various indentation levels (Catalina format)
                    if indent >= 2 and stripped.endswith(":") and len(stripped) > 1:
                        device_name = stripped.rstrip(":")

                        # Comprehensive skip list for system entries and technical details
                        skip_patterns = [
                            "bluetooth", "controller", "adapter", "address", "vendor", "product",
                            "firmware", "chipset", "transport", "supported services", "minor type",
                            "hardware", "features", "settings", "devices", "services", "paired",
                            "configured", "etc", "file transfer", "file exchange", "internet sharing",
                            "incoming", "outgoing", "serial ports"
                        ]

                        # Skip system entries, MAC addresses, and technical details
                        if (any(skip in device_name.lower() for skip in skip_patterns) or
                            len(device_name.replace("-", "").replace(":", "")) == 12 or  # MAC address
                            device_name.startswith("0x") or device_name.isdigit()):
                            continue

                        # Only accept names that look like real device names
                        if (len(device_name) > 3 and
                            any(c.isalpha() for c in device_name) and
                            not all(c in "0123456789abcdefABCDEF-:" for c in device_name)):
                            print(f"DEBUG: Found legacy format device '{device_name}'")
                            all_devices.append(device_name)

                    # Pattern 2: Look for device indicators in older macOS versions
                    elif any(indicator in stripped.lower() for indicator in ["paired device", "connected device"]):
                        # Try to extract device name from context (but be very careful)
                        if (":" in stripped and
                            not any(tech in stripped.lower() for tech in
                                ["address:", "rssi:", "vendor:", "product:", "services:", "firmware:"])):
                            parts = stripped.split(":")
                            if len(parts) >= 2:
                                potential_name = parts[0].strip()
                                # Very strict validation for extracted names
                                if (len(potential_name) > 3 and
                                    any(c.isalpha() for c in potential_name) and
                                    not any(skip in potential_name.lower() for skip in
                                        ["bluetooth", "controller", "address", "vendor", "product",
                                         "state", "hardware", "features", "settings", "devices", "services"])):
                                    print(f"DEBUG: Found device from description '{potential_name}'")
                                    all_devices.append(potential_name)

                    # Pattern 3: Catalina-specific - look for device names in different structure
                    elif (indent == 6 and stripped and not stripped.endswith(":") and
                          len(stripped) > 3 and
                          any(c.isalpha() for c in stripped) and  # Must contain letters
                          not any(tech in stripped.lower() for tech in
                              ["address", "vendor", "product", "firmware", "chipset", "state",
                               "discoverable", "services", "hardware", "features", "settings",
                               "bluetooth", "file transfer", "internet sharing", "serial ports"])):
                        # This might be a device name in Catalina format
                        print(f"DEBUG: Found potential Catalina device '{stripped}'")
                        all_devices.append(stripped)

            # If we found devices but no sections, assume they're connected
            if all_devices and not devices_connected and not devices_not_connected:
                # Final validation to filter out any system entries that might have slipped through
                validated_devices = []
                for device in all_devices:
                    # Skip system entries, MAC addresses, and technical details
                    skip_patterns = [
                        "bluetooth", "controller", "adapter", "hardware", "features",
                        "settings", "devices", "services", "paired", "configured", "etc",
                        "file transfer", "file exchange", "internet sharing", "incoming",
                        "outgoing", "serial ports"
                    ]

                    # Only keep real device names
                    if (not any(skip in device.lower() for skip in skip_patterns) and
                        len(device) > 3 and
                        any(c.isalpha() for c in device) and
                        not all(c in "0123456789abcdefABCDEF-:" for c in device)):
                        validated_devices.append(device)

                devices_connected = validated_devices
                print(f"DEBUG: Moved {len(validated_devices)} validated devices to connected (no sections found)")

            # Additional fallback: try alternative Bluetooth commands
            if not devices_connected and not devices_not_connected:
                print("DEBUG: Trying alternative Bluetooth detection methods...")

                # Method 1: Check Bluetooth preferences
                try:
                    bt_prefs = subprocess.check_output("defaults read /Library/Preferences/com.apple.Bluetooth 2>/dev/null || echo 'none'",
                                                     shell=True, text=True, timeout=5)
                    if "DeviceCache" in bt_prefs or "PairedDevices" in bt_prefs:
                        # Only add the generic message if no real devices found
                        if not all_devices:
                            all_devices.append("Bluetooth devices detected (detailed parsing unavailable on this macOS version)")
                            print("DEBUG: Found Bluetooth devices via preferences")
                except:
                    pass

                # Method 2: Check for blueutil (third-party tool)
                try:
                    blueutil_output = subprocess.check_output("which blueutil && blueutil --paired 2>/dev/null",
                                                            shell=True, text=True, timeout=5)
                    if blueutil_output.strip():
                        for line in blueutil_output.split('\n'):
                            if 'name:' in line:
                                name_part = line.split('name:')[1].strip().strip('"')
                                if name_part:
                                    devices_connected.append(name_part)
                                    print(f"DEBUG: Found device via blueutil: {name_part}")
                except:
                    pass

            # Format results
            result_lines = []
            if devices_connected:
                result_lines.append(f"Connected Bluetooth devices ({len(devices_connected)}):")
                result_lines.extend(devices_connected)
            if devices_not_connected:
                if devices_connected:
                    result_lines.append("")
                result_lines.append(f"Not Connected Bluetooth devices ({len(devices_not_connected)}):")
                result_lines.extend(devices_not_connected)

            if not devices_connected and not devices_not_connected:
                return "No bluetooth devices found."

            return "\n".join(result_lines)

        except subprocess.CalledProcessError:
            return "Failed to check Bluetooth devices."
        except subprocess.TimeoutExpired:
            return "Bluetooth devices check timed out."
    def check_profiles_installed(self):
        """Check for installed profiles - simplified and fast"""
        try:
            # Simple approach: try system_profiler first (no sudo needed)
            try:
                output = subprocess.check_output("system_profiler SPConfigurationProfileDataType",
                                               shell=True, text=True, timeout=30)
                if output and output.strip() and "No profiles" not in output:
                    # Basic parsing - look for profile indicators
                    lines = output.split('\n')
                    profiles = []
                    for line in lines:
                        line = line.strip()
                        if ('_computerlevel' in line or '_userlevel' in line or
                            'DisplayName:' in line or 'Identifier:' in line):
                            profiles.append(line)

                    if profiles:
                        return f"Found {len(profiles)} profile entries:\n" + "\n".join(profiles[:10])  # Limit to 10

            except Exception:
                pass

            # Fallback: simple directory check
            try:
                config_dir = "/var/db/ConfigurationProfiles/Settings"
                if os.path.exists(config_dir):
                    files = os.listdir(config_dir)
                    plist_files = [f for f in files if f.endswith('.plist') and 'cloud' not in f.lower()]
                    if plist_files:
                        return f"Found {len(plist_files)} configuration profile files"
            except Exception:
                pass

            return "No profiles installed on this device."

        except Exception as e:
            return f"Failed to check installed profiles: {str(e)}"


    def on_window_resize(self, event):
        """Handle window resize events"""
        if event.widget == self:
            # Ensure minimum window size
            if self.winfo_height() < 300:
                self.geometry(f"{self.winfo_width()}x300")
    
    def on_closing(self):
        logging.info("on_closing called")
        print("on_closing called")
        try:
            # Stop all periodic updates
            self.blink_status = False
            # Destroy the window
            self.quit()
            self.destroy()
        except Exception as e:
            logging.debug(f"Error during closing: {str(e)}")
            print(f"DEBUG: Error during closing: {e}")
    def show_about_dialog(self):
        # Custom About dialog with app icon
        import customtkinter as ctk
        about_win = ctk.CTkToplevel(self)
        about_win.title(f"About {APP_NAME}")
        about_win.geometry("360x200")
        about_win.resizable(False, False)
        # Set icon if possible (macOS: .icns, Windows: .ico)
        icon_path = get_resource_path("img/vsmactool.icns")
        try:
            if os.path.exists(icon_path):
                # On macOS, iconbitmap works with .icns in Tk 8.6.9+
                about_win.iconbitmap(icon_path)
        except Exception:
            pass
        about_win.grab_set()
        about_win.configure(fg_color="#232323")
        # Icon
        try:
            from PIL import Image
            if os.path.exists(icon_path):
                img = Image.open(icon_path)
                img = img.resize((48, 48))
                icon_img = ctk.CTkImage(light_image=img, dark_image=img, size=(48, 48))
                icon_label = ctk.CTkLabel(about_win, image=icon_img, text="", fg_color="transparent")
                icon_label.pack(pady=(18, 2))
        except Exception:
            pass
        # App name and version
        ctk.CTkLabel(about_win, text=APP_NAME, font=("SF Pro Display", 18, "bold"), text_color="#73bee6", fg_color="transparent").pack(pady=(2, 0))
        ctk.CTkLabel(about_win, text=f"Version {APP_VERSION}", font=("SF Pro", 13), text_color="#B3B3B3", fg_color="transparent").pack(pady=(0, 6))
        ctk.CTkLabel(about_win, text="© 2025 vonzki", font=("SF Pro", 12), text_color="#686464", fg_color="transparent").pack(pady=(0, 10))
        ctk.CTkLabel(about_win, text="All rights reserved.", font=("SF Pro", 11), text_color="#686464", fg_color="transparent").pack(pady=(0, 10))
        ctk.CTkButton(about_win, text="OK", command=about_win.destroy, width=80, fg_color="#007AFF", hover_color="#0056CC").pack(pady=(0, 10))
    def get_smb_url(self):
        """Return the SMB URL for connecting (with credentials if needed)"""
        server = self.network_config["server"]
        share = self.network_config["share"]
        username = self.network_config["username"]
        password = self.network_config["password"]
        if username and password:
            return f"smb://{username}:{password}@{server}/{share}"
        elif username:
            return f"smb://{username}@{server}/{share}"
        else:
            return f"smb://{server}/{share}"

    def get_share_display_string(self):
        """Return a user-friendly display string for the share (no credentials)"""
        server = self.network_config["server"]
        share = self.network_config["share"]
        return f"//{server}/{share}"

    def run_bluetooth_removal_in_terminal(self):
        """Open a new Terminal window and run Bluetooth removal commands using ~/rmbt.py and AppleScript."""
        import subprocess
        import os
        import tempfile
        print("DEBUG: Starting Bluetooth removal process using AppleScript (robust shell script method)...")
        # Write rmbt.py to ~/rmbt.py
        rmbt_path = os.path.expanduser("~/rmbt.py")
        rmbt_code = '''#!/usr/bin/python
from Foundation import NSBundle

IOBluetooth = NSBundle.bundleWithIdentifier_('com.apple.Bluetooth')
IOBluetoothDevice = IOBluetooth.classNamed_('IOBluetoothDevice')
# remove configured devices
try:
    devices = list(IOBluetoothDevice.configuredDevices())
except:
    devices = []
for device in devices:
    try:
        device.remove()
    except:
        pass
# remove paired devices
try:
    devices = list(IOBluetoothDevice.pairedDevices())
except:
    devices = []
for device in devices:
    try:
        device.remove()
    except:
        pass
# remove favorite devices
try:
    devices = list(IOBluetoothDevice.favoriteDevices())
except:
    devices = []
for device in devices:
    try:
        device.remove()
    except:
        pass
'''
        try:
            with open(rmbt_path, 'w') as f:
                f.write(rmbt_code)
            os.chmod(rmbt_path, 0o755)
            print(f"DEBUG: Created rmbt.py at {rmbt_path}")
        except Exception as e:
            logging.error(f"Failed to write rmbt.py: {str(e)}")
            print(f"DEBUG: Failed to write rmbt.py: {e}")
            self.display_result(f"Error: Could not write rmbt.py to {rmbt_path}. Please check permissions.")
            return
        SUDO_PASSWORD = globals().get('SUDO_PASSWORD', '1111')
        # Write the shell block to a temp .sh file
        shell_block = f'''
echo 'Removing Bluetooth devices...'
sudo -S -p '' -k < /dev/null  # clear cached sudo password
echo {SUDO_PASSWORD} | sudo -S /usr/bin/python {rmbt_path}
echo 'Killing bluetoothd...'
echo {SUDO_PASSWORD} | sudo -S pkill bluetoothd
echo {SUDO_PASSWORD} | sudo -S pkill bluetoothd
echo 'Removing preferences...'
echo {SUDO_PASSWORD} | sudo -S rm /Library/Preferences/com.apple.Bluetooth.plist
echo {SUDO_PASSWORD} | sudo -S pkill bluetoothd
echo 'Done.'
echo ''
echo '=== Process Completed ==='
echo 'If devices remain, remove manually in System Settings > Bluetooth'
echo 'Note: On modern versions of macOS, paired or registered Bluetooth devices may not be fully removed by script.'
echo 'You may need to manually remove devices in System Settings > Bluetooth for complete removal.'
echo ''
'''
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False) as f:
            f.write(shell_block)
            shell_script_path = f.name
        os.chmod(shell_script_path, 0o755)
        # AppleScript to run the shell script in Terminal (Catalina compatible)
        applescript = f'''
tell application "Terminal"
    try
        close every window
    end try
    delay 1
    activate
    do script "exec bash {shell_script_path}"
    delay 1
    try
        set bounds of front window to {{30, 0, 600, 250}}
    end try
    try
        set current settings of selected tab of front window to settings set "Homebrew"
    on error
        -- Ignore if Homebrew settings don't exist (Catalina compatibility)
    end try
end tell
'''
        # Try multiple methods to ensure Terminal opens on different MacBook configurations
        terminal_opened = False

        # Method 1: Try the standard AppleScript approach
        try:
            print("DEBUG: Method 1 - Executing standard AppleScript for Bluetooth removal...")
            result = subprocess.run(['osascript', '-e', applescript], capture_output=True, text=True, timeout=30)
            print(f"DEBUG: Method 1 AppleScript result: {result.returncode}")
            if result.stdout:
                print(f"DEBUG: Method 1 AppleScript stdout: {result.stdout}")
            if result.stderr:
                print(f"DEBUG: Method 1 AppleScript stderr: {result.stderr}")

            if result.returncode == 0:
                terminal_opened = True
                print("DEBUG: Method 1 succeeded - Terminal should be open")
            else:
                print("DEBUG: Method 1 failed, trying alternative methods...")

        except subprocess.TimeoutExpired:
            print("DEBUG: Method 1 timed out, trying alternative methods...")
        except Exception as e:
            print(f"DEBUG: Method 1 failed with exception: {e}, trying alternative methods...")

        # Method 2: Try simplified AppleScript (for older macOS versions)
        if not terminal_opened:
            try:
                print("DEBUG: Method 2 - Trying simplified AppleScript...")
                simple_applescript = f'''
tell application "Terminal"
    activate
    do script "exec bash {shell_script_path}"
end tell
'''
                result = subprocess.run(['osascript', '-e', simple_applescript], capture_output=True, text=True, timeout=20)
                print(f"DEBUG: Method 2 AppleScript result: {result.returncode}")
                if result.returncode == 0:
                    terminal_opened = True
                    print("DEBUG: Method 2 succeeded - Terminal should be open")
                else:
                    print(f"DEBUG: Method 2 failed: {result.stderr}")
            except Exception as e:
                print(f"DEBUG: Method 2 failed with exception: {e}")

        # Method 3: Try using 'open' command with Terminal
        if not terminal_opened:
            try:
                print("DEBUG: Method 3 - Trying 'open' command with Terminal...")
                open_cmd = f'open -a Terminal "{shell_script_path}"'
                result = subprocess.run(open_cmd, shell=True, capture_output=True, text=True, timeout=15)
                print(f"DEBUG: Method 3 'open' command result: {result.returncode}")
                if result.returncode == 0:
                    terminal_opened = True
                    print("DEBUG: Method 3 succeeded - Terminal should be open")
                else:
                    print(f"DEBUG: Method 3 failed: {result.stderr}")
            except Exception as e:
                print(f"DEBUG: Method 3 failed with exception: {e}")

        # Method 4: Try direct terminal command execution
        if not terminal_opened:
            try:
                print("DEBUG: Method 4 - Trying direct terminal command...")
                direct_cmd = f'osascript -e \'tell app "Terminal" to do script "bash {shell_script_path}"\''
                result = subprocess.run(direct_cmd, shell=True, capture_output=True, text=True, timeout=15)
                print(f"DEBUG: Method 4 direct command result: {result.returncode}")
                if result.returncode == 0:
                    terminal_opened = True
                    print("DEBUG: Method 4 succeeded - Terminal should be open")
                else:
                    print(f"DEBUG: Method 4 failed: {result.stderr}")
            except Exception as e:
                print(f"DEBUG: Method 4 failed with exception: {e}")

        # Method 5: Last resort - try to run the script directly and show instructions
        if not terminal_opened:
            try:
                print("DEBUG: Method 5 - Last resort, trying to run script directly...")
                # Try to execute the script directly in background and show manual instructions
                bg_cmd = f'nohup bash "{shell_script_path}" > /tmp/bluetooth_removal.log 2>&1 &'
                result = subprocess.run(bg_cmd, shell=True, capture_output=True, text=True, timeout=5)
                print(f"DEBUG: Method 5 background execution result: {result.returncode}")

                self.display_result(
                    "Bluetooth Removal - Manual Method:\n"
                    f"Terminal could not be opened automatically. The script has been created at:\n"
                    f"{shell_script_path}\n\n"
                    f"Please manually:\n"
                    f"1. Open Terminal application\n"
                    f"2. Run: bash {shell_script_path}\n"
                    f"3. Or run: python {rmbt_path}\n\n"
                    f"Alternative: Check /tmp/bluetooth_removal.log for background execution results."
                )
                return
            except Exception as e:
                print(f"DEBUG: Method 5 failed with exception: {e}")

        # If we get here and terminal_opened is True, success!
        if terminal_opened:
            print("DEBUG: Terminal successfully opened using one of the methods")
        else:
            print("DEBUG: All methods failed to open Terminal")
            self.display_result(
                "Error: Could not open Terminal automatically.\n"
                f"Please manually run: python {rmbt_path}\n"
                f"Or run: bash {shell_script_path}"
            )
        # Removed duplicate self.display_result call here

def find_existing_share_mount(server, share_name):
    """Return the mount point if the SMB share is already mounted anywhere in /Volumes, else None."""
    try:
        output = subprocess.check_output("mount", shell=True, text=True)
        for line in output.splitlines():
            # Look for the correct server and share name in the mount string
            if f"{server}" in line and f"/{share_name}" in line:
                # Example: //it-user-02@***********/Share_IT on /Volumes/Share_IT (smbfs, ...)
                parts = line.split(" on ")
                if len(parts) > 1:
                    mount_point = parts[1].split(" (")[0]
                    if os.path.exists(mount_point):
                        return mount_point
    except Exception as e:
        logging.debug(f"Error checking existing share mount: {str(e)}")
        pass
    return None

def is_mount_point_for_share(mount_point, server, share_name):
    try:
        output = subprocess.check_output("mount", shell=True, text=True)
        for line in output.splitlines():
            if mount_point in line and f"{server}" in line and f"/{share_name}" in line:
                return True
    except Exception as e:
        logging.debug(f"Error checking mount point for share: {str(e)}")
        pass
    return False

def prepare_mount_point(mount_point, server, share_name):
    import subprocess, os
    # Check if the share is already mounted anywhere
    existing = find_existing_share_mount(server, share_name)
    if existing:
        return existing
    # If the mount point exists but is not a mount, try to remove it
    if os.path.exists(mount_point):
        output = subprocess.check_output("mount", shell=True, text=True)
        if mount_point not in output:
            try:
                os.rmdir(mount_point)
            except Exception:
                # If any error, use alternate
                alt = mount_point + "_2"
                try:
                    if not os.path.exists(alt):
                        os.makedirs(alt, exist_ok=True)
                    return alt
                except Exception:
                    from tkinter import messagebox
                    messagebox.showerror(
                        "Permission Error",
                        f"Cannot access or remove {mount_point} or {alt}. Please remove them manually or run the app as admin."
                    )
                    raise
    # Create if doesn't exist
    if not os.path.exists(mount_point):
        try:
            os.makedirs(mount_point, exist_ok=True)
        except Exception:
            # If cannot create, use alternate
            alt = mount_point + "_2"
            if not os.path.exists(alt):
                os.makedirs(alt, exist_ok=True)
            return alt
    # Final check: can we write to it?
    if not os.access(mount_point, os.W_OK):
        alt = mount_point + "_2"
        if not os.path.exists(alt):
            os.makedirs(alt, exist_ok=True)
        return alt
    return mount_point

def main():
    print("DEBUG: Entering main()")
    app = InternetCheckerApp()
    print("DEBUG: InternetCheckerApp instantiated, calling mainloop()")
    app.mainloop()
    print("DEBUG: mainloop() exited")

print('=== VS Mac Intel Checker: Script started ===')
try:
    if __name__ == "__main__":
        main()
except Exception as e:
    import traceback
    with open(os.path.expanduser("~/Library/Logs/vs_mac_security_checker/vs_mac_security_checker.log"), "a") as f:
        f.write("\nFATAL ERROR:\n" + traceback.format_exc() + "\n")
    print("FATAL ERROR:", e)
    print(traceback.format_exc())

# Add a warning if not running in the correct venv Python
import sys, os
if __name__ == "__main__":
    venv_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "venv"))
    if not sys.executable.startswith(venv_path):
        print("\033[91mWARNING: You are not running with the virtual environment Python!\033[0m")
        print(f"Current Python: {sys.executable}")
        print(f"Expected to start with: {venv_path}")
        print("Run with: venv/bin/python3 mac_chcker.py\n")
