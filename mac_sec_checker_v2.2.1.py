# Last Updated:07-02-2025 11:36:54
import sys
import os
import traceback
import logging
# pyinstaller --name="VSMac_Security_Checker_0702_1618" --windowed --icon=img/vsmactool.icns --add-data "img:img" --clean --noconfirm vs_mac_sec_checker_v2.2.1.py
# pyinstaller --name="Mac_Security_Checker_0702_1617" --windowed --icon=img/vsmactool.icns --add-data "img:img" --clean --noconfirm --collect-all customtkinter vs_mac_sec_checker_v2.2.1.py
# Set up basic logging first, before any other imports 
log_dir = os.path.expanduser("~/Library/Logs/vs_mac_security_checker")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, "vs_mac_security_checker.log")

logging.basicConfig(
    filename=log_file,
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def log_error_and_exit(error_msg, exc_info=None):
    logging.error(error_msg)
    if exc_info:
        logging.error(traceback.format_exc())
    sys.exit(1)

try:
    logging.info("Starting application initialization")
    import socket
    import subprocess
    import customtkinter as ctk
    import threading
    import time
    import csv
    import xml.dom.minidom
    import xml.etree.ElementTree as ET
    from tkinter import filedialog
    from datetime import datetime
    import queue
    from PIL import Image
    import pathlib
    import platform
    import getpass
    from password_dialog import get_sudo_password

    # Try importing keyring, but don't fail if it's not available
    try:
        import keyring
        KEYRING_AVAILABLE = True
        logging.info("Keyring module successfully imported")
    except Exception as e:
        KEYRING_AVAILABLE = False
        logging.warning(f"Keyring module not available or failed to load: {str(e)}")
        print("Warning: keyring module not available or failed to load. Some features may be limited.")

    # Disable Tkinter menu bar creation which causes crashes on macOS vs
    os.environ['TKINTER_NO_MACOS_MENUBAR'] = '1'
    logging.info("Environment variables set")

    # Force dark mode for all CustomTkinter windows
    ctk.set_appearance_mode("dark")

except Exception as e:
    log_error_and_exit(f"Failed during initial imports: {str(e)}", exc_info=True)

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    base_path = getattr(sys, '_MEIPASS', os.path.abspath('.'))
    return os.path.join(base_path, relative_path)

# Default passwords
SUDO_PASSWORD = "1111"  # Default sudo password, will try this first
WIFI_PASSWORD = None  # WiFi password for automatic connection

# Check macOS version
def get_macos_version():
    """Get the macOS version as a tuple of integers (major, minor, patch)"""
    version_str = platform.mac_ver()[0]
    version_parts = version_str.split('.')
    # Ensure we have at least 3 parts, padding with zeros if needed
    while len(version_parts) < 3:
        version_parts.append('0')
    return tuple(int(part) for part in version_parts[:3])

# Get macOS version
MACOS_VERSION = get_macos_version()

# Utility for macOS version checks
import platform

def get_macos_version_tuple():
    version_str = platform.mac_ver()[0]
    return tuple(map(int, (version_str.split("."))))

def check_compatibility():
    version = get_macos_version_tuple()
    # Only require macOS 10.15 or newer, do not check minor/patch strictly
    if version[0] < 10 or (version[0] == 10 and version[1] < 15):
        print("This application requires macOS Catalina (10.15) or newer.")
        sys.exit(1)
    try:
        chip = subprocess.check_output("sysctl -n machdep.cpu.brand_string", shell=True, text=True).strip()
    except Exception:
        chip = "Unknown CPU"
    print(f"Running on: {chip}, macOS {'.'.join(map(str, version))}")

class InternetCheckerApp(ctk.CTk):
    def __init__(self):
        print("=== InternetCheckerApp initializing ===")
        try:
            logging.info("Initializing InternetCheckerApp")
            super().__init__()
            logging.info("Base class initialized")
            
            # Network share configuration
            self.network_path = "/Volumes/share"  # Lowercase 's' to match SMB URL
            self.server_address = "**************"
            self.share_name = "share"  # Lowercase 's' to match SMB URL
            
            # Add network_config dictionary
            self.network_config = {
                "mount_point": self.network_path,
                "server": self.server_address,
                "share": self.share_name
            }
            
            logging.info("Setting up UI components")
            # Load icons from the img folder using resource path
            self.icons = {
                "bluetooth": self.load_icon(get_resource_path("img/bluetooth.icns"), (20,20)),
                "findmy": self.load_icon(get_resource_path("img/findmy.icns"), (20,20)),
                "profile": self.load_icon(get_resource_path("img/device.icns"), (20,20)),
                "system": self.load_icon(get_resource_path("img/sysinfo.icns"), (20,20)),
                "shutdown": self.load_icon(get_resource_path("img/shutdown.png"), (20,20)),
                "erase": self.load_icon(get_resource_path("img/erase.png"), (20,20)),
                "exit": self.load_icon(get_resource_path("img/exit.png"), (20, 20))
            }
            logging.info(f"Icons loaded: {self.icons}")

            logging.info("Setting up window properties")
            # Set window properties
            self.title("VS Mac Security Checker v2.2.1")
            self.geometry("1120x755")
            self.minsize(800, 300) # Adjusted minimum height for new layout
            self.maxsize(2000, 850) # Adjusted maximum height for new layout

            
            # Configure main window grid (self)
            self.grid_columnconfigure(0, weight=1)  # Only one main content column that expands horizontally
            self.grid_rowconfigure(0, weight=1)  # Top content area expands vertically
            self.grid_rowconfigure(1, weight=0)  # Status bar row fixed at bottom (no vertical expansion)
            
            logging.info("Creating main frame")
            # Create main frame as the primary content container for the top part of the window
            self.main_frame = ctk.CTkFrame(self) # Parent is self
            self.main_frame.grid(row=0, column=0, padx=(0,0), pady=1, sticky="nsew")
            
            # Configure main_frame grid (internal layout for left and right panels)
            self.main_frame.grid_columnconfigure(0, weight=0) # Left panel column (fixed width)
            self.main_frame.grid_columnconfigure(1, weight=1) # Right panel column (expands horizontally)
            self.main_frame.grid_rowconfigure(0, weight=1)    # Single row for content, allowing right panel to expand vertically
            
            logging.info("Creating Left Panel Frame")
            # Create Left Panel Frame to hold Process, System Info, and Shortcuts frames
            self.left_panel_frame = ctk.CTkScrollableFrame(
                self.main_frame,
                fg_color="transparent",
                width=260,
                height=500
            )
            self.left_panel_frame.grid(row=0, column=0, padx=(0,0), pady=0, sticky="nsew")
            self.left_panel_frame.grid_columnconfigure(0, weight=1)

            logging.info("Creating Process Steps Frame")
            # Process Steps Frame with macOS-style border - FIXED HEIGHT
            self.process_frame = ctk.CTkFrame(
                self.left_panel_frame,
                width=280,
                height=215,
                fg_color="#2A2A2A",
                border_width=1,
                border_color="#404040",
                corner_radius=8
            )
            self.process_frame.grid(row=0, column=0, padx=(0,0), pady=(0, 1), sticky="ew")
            self.process_frame.grid_propagate(False)
            self.process_frame.grid_columnconfigure(0, weight=1)

            logging.info("Creating Process Steps Header")
            # Process steps header
            self.process_steps_label = ctk.CTkLabel(
                self.process_frame,
                text="Security Lock Checks",
                font=("SF Pro Display", 16, "bold"),
                text_color="#73bee6"
            )
            self.process_steps_label.grid(row=0, column=0, padx=20, pady=(1, 0), sticky="nsew")

            logging.info("Creating Buttons and Checkboxes")
            # Define buttons and their corresponding functions
            self.buttons = [
                ("Check Time on time.apple.com", self.check_apple_time),
                ("Check Apple Server Connection", self.check_apple_server_connection),
                ("Check ADE/DEP Enrollment", self.check_dep_enrollment),
                ("Check MDM Enrollment Status", lambda: self.run_sudo_command("sudo profiles status -type enrollment")),
                ("Check Installed Profiles", self.check_profiles_installed),
                ("Show Device Enrollment Log", self.show_device_enrollment_log),
                ("Remove Paired Bluetooth Devices", self.delete_bluetooth_devices)
            ]

            # Checkboxes for process steps with improved styling
            self.checkboxes = []
            for i, (text, _) in enumerate(self.buttons):
                checkbox = ctk.CTkCheckBox(
                    self.process_frame,
                    text=text,
                    state="disabled",
                    width=20,
                    height=20,
                    checkbox_width=16,
                    checkbox_height=16,
                    font=("SF Mono", 12),
                    border_width=1,
                    text_color="#FFFFFF",
                    text_color_disabled="#FFFFFF",
                    fg_color="#007AFF",
                    hover_color="#0056CC"
                )
                checkbox.grid(row=i+1, column=0, padx=8, pady=1, sticky="w")
                checkbox.configure(text_color="#FFFFFF")
                self.checkboxes.append(checkbox)

            logging.info("Creating Execute All Button")
            # Execute All button with improved styling
            self.execute_all_button = ctk.CTkButton(
                self.process_frame,
                text="Recheck All",
                command=self.auto_execute,
                width=100,
                height=22,
                anchor="center",
                font=("SF Pro", 12),
                fg_color="#007AFF",
                hover_color="#0056CC",
                corner_radius=4
            )
            self.execute_all_button.grid(row=len(self.buttons) + 1, column=0, padx=65, pady=(3, 3), sticky="w")

            logging.info("Creating System Information Frame")
            # System Information Frame with macOS-style border - FIXED HEIGHT
            self.hardware_frame = ctk.CTkFrame(
                self.left_panel_frame,
                width=260,
                height=260,
                fg_color="#2A2A2A",
                border_width=1,
                border_color="#404040",
                corner_radius=8
            )
            self.hardware_frame.grid(row=1, column=0, padx=(0,0), pady=(0, 1), sticky="ew")
            self.hardware_frame.grid_propagate(False)
            self.hardware_frame.grid_columnconfigure(0, weight=1)
            self.hardware_frame.grid_columnconfigure(1, weight=2)

            logging.info("Creating System Information Header")
            # System information header
            self.hardware_label = ctk.CTkLabel(
                self.hardware_frame,
                text="System Information",
                font=("SF Pro Display", 16, "bold"),
                text_color="#73bee6"
            )
            self.hardware_label.grid(row=0, column=0, columnspan=2, padx=20, pady=1, sticky="nsew")

            # Initialize hardware_info here before it's used
            self.hardware_info = {
                "Model Identifier": None,
                "Chip": None,
                "Memory": None,
                "SSD Storage": None,
                "Serial number": None,
                "macOS Version": None,
                "Activation Lock": None
            }

            logging.info("Creating System Information Labels")
            # System information labels with improved styling
            self.hardware_info_entries = {}
            for i, (key, _) in enumerate(self.hardware_info.items()):
                # Label for the key
                key_label = ctk.CTkLabel(
                    self.hardware_frame,
                    text=key + ":",
                    font=("SF Pro", 11),
                    text_color="#B3B3B3",
                    anchor="w"
                )
                key_label.grid(row=i+1, column=0, padx=(5, 5), pady=0, sticky="e")
                # Entry for the value (readonly, allows horizontal scrolling by cursor)
                value_entry = ctk.CTkEntry(
                    self.hardware_frame,
                    border_width=1,
                    font=("SF Pro", 11),
                    text_color="#FFFFFF",
                    width=160,
                    height=22,
                    corner_radius=4
                )
                value_entry.grid(row=i+1, column=1, padx=(0, 3), pady=0, sticky="w")
                value_entry.configure(state="readonly")
                self.hardware_info_entries[key] = value_entry
            # Create a frame for the save buttons with transparent background
            self.save_buttons_frame = ctk.CTkFrame(
                self.hardware_frame,
                fg_color="transparent"
            )
            self.save_buttons_frame.grid(row=len(self.hardware_info)+1, column=0, columnspan=2, pady=5, padx=5, sticky="ew")
            self.save_buttons_frame.grid_columnconfigure(0, weight=1)
            self.save_buttons_frame.grid_columnconfigure(1, weight=1)

            # Save to CSV button with improved styling
            self.save_csv_button = ctk.CTkButton(
                self.save_buttons_frame,
                text="Save to CSV",
                command=self.save_hardware_overview,
                font=("SF Pro", 11),
                fg_color="#007AFF",
                hover_color="#0056CC",
                text_color="white",
                corner_radius=4,
                width=90,
                height=22
            )
            self.save_csv_button.grid(row=0, column=0, pady=0, padx=(0, 1), sticky=" ")

            # Save to XML button with improved styling
            self.save_xml_button = ctk.CTkButton(
                self.save_buttons_frame,
                text="Save to XML",
                command=self.save_hardware_to_xml,
                font=("SF Pro", 11),
                fg_color="#007AFF",
                hover_color="#0056CC",
                text_color="white",
                corner_radius=4,
                width=90,
                height=22
            )
            self.save_xml_button.grid(row=0, column=1, pady=0, padx=(1, 0), sticky=" ")

            logging.info("Creating Shortcuts Frame")
            # Shortcuts Frame with macOS-style border - FIXED HEIGHT
            self.shortcut_frame = ctk.CTkFrame(
                self.left_panel_frame,
                width=260,
                height=225,
                fg_color="#2A2A2A",
                border_width=1,
                border_color="#404040",
                corner_radius=4
            )
            self.shortcut_frame.grid(row=2, column=0, padx=(0,0), pady=(0, 0), sticky="ew")
            self.shortcut_frame.grid_propagate(False)
            self.shortcut_frame.grid_columnconfigure(0, weight=1)

            logging.info("Creating Shortcuts Header")
            # Shortcuts header
            self.shortcuts_label = ctk.CTkLabel(
                self.shortcut_frame,
                text="Shortcuts",
                font=("SF Pro Display", 16, "bold"),
                text_color="#73bee6"
            )
            self.shortcuts_label.grid(row=0, column=0, padx=20, pady=1, sticky="nsew")

            logging.info("Creating Shortcuts Buttons")
            # Define macOS style button colors for shortcuts
            macos_button_color = "#333333"  # Slightly lighter than frame background
            macos_button_hover_color = "#404040"  # Even lighter for hover state

            # Bluetooth button with improved styling
            self.open_bluetooth_button = ctk.CTkButton(
                self.shortcut_frame,
                text="Bluetooth",
                image=self.icons["bluetooth"],
                command=self.open_bluetooth_settings,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.open_bluetooth_button.grid(row=1, column=0, padx=10, pady=2, sticky="ew")

            # FindMy button with improved styling
            self.open_findmy_button = ctk.CTkButton(
                self.shortcut_frame,
                text="FindMy",
                image=self.icons["findmy"],
                command=self.open_findmy,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.open_findmy_button.grid(row=2, column=0, padx=10, pady=2, sticky="ew")

            # Profile button with improved styling
            self.open_profile_button = ctk.CTkButton(
                self.shortcut_frame,
                text="Device Management Profile",
                image=self.icons["profile"],
                command=self.open_system_preferences,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.open_profile_button.grid(row=3, column=0, padx=10, pady=2, sticky="ew")

            # System Information button with improved styling
            self.sys_info_button = ctk.CTkButton(
                self.shortcut_frame,
                text="System Information",
                image=self.icons["system"],
                command=self.show_system_info,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.sys_info_button.grid(row=4, column=0, padx=10, pady=2, sticky="ew")

            # Erase all content button with improved styling
            self.erase_button = ctk.CTkButton(
                self.shortcut_frame,
                text="Erase all content and settings..",
                image=self.icons["erase"],
                command=self.erase_app,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.erase_button.grid(row=5, column=0, padx=10, pady=2, sticky="ew")

            # Shutdown button with improved styling
            self.shutdown_button = ctk.CTkButton(
                self.shortcut_frame,
                text="Shutdown System",
                image=self.icons["shutdown"],
                command=self.shutdown_system,
                compound="left",
                width=220,
                height=28,
                anchor="w",
                font=("SF Pro", 12),
                fg_color=macos_button_color,
                hover_color=macos_button_hover_color,
                corner_radius=4
            )
            self.shutdown_button.grid(row=6, column=0, padx=10, pady=1, sticky="ew")

            # Right Frame (Process Logs) with macOS-style border
            self.right_frame = ctk.CTkFrame(
                self.main_frame,
                fg_color="#2A2A2A",
                border_width=1,
                border_color="#404040",
                corner_radius=4,
                height=490  # Set fixed height
            )
            self.right_frame.grid(row=0, column=1, padx=(0,0), pady=(3,3), sticky="nsew") # Occupies right column
            self.right_frame.grid_propagate(False)  # Prevent frame from resizing based on content
            self.right_frame.grid_columnconfigure(0, weight=1)  # Make width flexible
            self.right_frame.grid_rowconfigure(1, weight=1)  # Allow content to expand vertically within fixed height

            # Process logs header
            self.process_logs_label = ctk.CTkLabel(
                self.right_frame,
                text="Process Logs",
                font=("SF Pro Display", 16, "bold"),
                text_color="#73bee6"
            )
            self.process_logs_label.grid(row=0, column=0, padx=5, pady=(1, 0), sticky="nsew")

            # Process logs textbox with improved styling
            self.result_text = ctk.CTkTextbox(
                self.right_frame,
                wrap="word",
                fg_color="#1A1A1A",
                border_width=1,
                border_color="#333333",
                corner_radius=4,
                font=("SF Mono", 12),
                text_color="#FFFFFF",
                activate_scrollbars=False # Disable internal scrollbars to use external one
            )
            self.result_text.grid(row=1, column=0, padx=(3,3), pady=(0, 1), sticky="nsew")

            # Add a scrollbar to the result_text
            self.result_text_scrollbar = ctk.CTkScrollbar(
                self.right_frame,
                command=self.result_text.yview,
                orientation="vertical"
            )
            self.result_text_scrollbar.grid(row=1, column=1, sticky="ns", padx=(0,0), pady=(0,1))
            self.result_text.configure(yscrollcommand=self.result_text_scrollbar.set)

            # Status bar frame with subtle border
            self.status_frame = ctk.CTkFrame(
                self, # Parent is self, so it's always at the bottom of the window
                fg_color="#2A2A2A",
                border_width=1,
                border_color="#404040",
                corner_radius=4,
                height=35
            )
            # Placed at row 1 of self (bottom row), spanning the single main content column
            self.status_frame.grid(row=1, column=0, padx=5, pady=(1, 3), sticky="ew") # Adjusted padx/pady
            self.status_frame.grid_propagate(False) # Prevent status frame from resizing
            
            # Configure status frame columns for left, center, right layout
            self.status_frame.grid_columnconfigure(0, weight=1) # Left section
            self.status_frame.grid_columnconfigure(1, weight=1) # Center section
            self.status_frame.grid_columnconfigure(2, weight=1) # Right section
            
            # Left section: Internet status with improved styling
            self.left_status_frame = ctk.CTkFrame(self.status_frame, fg_color="transparent")
            self.left_status_frame.grid(row=0, column=0, sticky="w", padx=(10, 5), pady=1)
            
            self.status_indicator = ctk.CTkCanvas(
                self.left_status_frame,
                width=16,
                height=16,
                bg="#2A2A2A",
                highlightthickness=0
            )
            self.status_indicator.pack(side="left", padx=(0, 8))
            self.status_light = self.status_indicator.create_oval(2, 2, 14, 14, fill="#34C759", outline="white", width=1)
            
            self.status_label = ctk.CTkLabel(
                self.left_status_frame,
                text="Internet connection",
                font=("SF Pro", 12),
                text_color="#B3B3B3"
            )
            self.status_label.pack(side="left")
            
            # Center section: Date and time with improved styling
            self.datetime_frame = ctk.CTkFrame(self.status_frame, fg_color="transparent")
            self.datetime_frame.grid(row=0, column=1, sticky="ns", pady=3)
            
            self.datetime_label = ctk.CTkLabel(
                self.datetime_frame,
                text="",
                font=("SF Mono", 12),
                text_color="#d3de5c"
            )
            self.datetime_label.pack()
            
            # Right section: Exit button and "by vonzki" label with improved styling
            self.right_status_frame = ctk.CTkFrame(self.status_frame, fg_color="transparent")
            self.right_status_frame.grid(row=0, column=2, sticky="e", padx=5, pady=3)
            
            self.vonzk_label = ctk.CTkLabel(
                self.right_status_frame,
                text="@2025 vonzki",
                font=("SF Pro", 10),
                text_color="#686464"
            )
            self.vonzk_label.pack(side="left", padx=(0, 30))
            
            # Exit button in status bar (color confirmed from image)
            self.exit_button = ctk.CTkButton(
                self.right_status_frame,
                image=self.icons["exit"],
                text="Exit",
                command=self.on_closing,
                width=90,
                height=25,
                fg_color="#007AFF", # Assuming this was the blue accent color
                hover_color="#D7554E",
                corner_radius=4,
                font=("SF Pro", 13)
            )
            self.exit_button.pack(side="right")
            
            # Bind window resize event (only for minsize enforcement, grid handles layout)
            self.bind("<Configure>", self.on_window_resize)
            
            self.result_queue = queue.Queue()
            self.after(100, self.check_result_queue)
            self.internet_connected = True
            self.update_datetime()
            self.check_internet_periodically()
            self.update_hardware_info()
            # Force white text color for checkboxes after initialization
            self.after(100, self.force_checkbox_white_text)
            # Start the auto-execute process automatically
            self.after(1000, self.auto_execute)  # Start after 1 second to allow GUI to initialize
            # Start the blinking effect
            self.blink_status = True
            self.blink_internet_status()
        except Exception as e:
            error_msg = f"Error in InternetCheckerApp initialization: {str(e)}\n{traceback.format_exc()}"
            logging.error(error_msg)
            raise  # Re-raise the exception to be caught by the main error handler
    def force_checkbox_white_text(self):
        """Force white text color for all checkboxes"""
        for checkbox in self.checkboxes:
            try:
                # Try different parameter names that might work
                checkbox.configure(text_color_disabled="#FFFFFF")
            except:
                try:
                    # Try accessing the internal text label directly
                    if hasattr(checkbox, '_text_label'):
                        checkbox._text_label.configure(fg="#FFFFFF")
                        checkbox._text_label.configure(text_color="#FFFFFF")
                except:
                    try:
                        # Try the tkinter approach
                        checkbox.configure(fg="#FFFFFF")
                    except Exception as e:
                        print(f"Could not set checkbox text color: {e}")
    def mount_network_share_at_startup(self):
        """Mount the network share at application startup"""
        try:
            # Get network configuration
            mount_point = self.network_config["mount_point"]
            server_address = self.network_config["server"]
            share_name = self.network_config["share"]
            smb_url = f"smb://{server_address}/{share_name}"
            # First, check if the share is already mounted
            if os.path.exists(mount_point) and os.path.isdir(mount_point):
                # Check if the mount point is actually mounted and not just an empty directory
                try:
                    # List files in the directory to verify it's mounted
                    os.listdir(mount_point)
                    print(f"Network share {smb_url} is already mounted at {mount_point}.")
                    return True
                except PermissionError:
                    # This is likely an empty mount point
                    print("Mount point exists but permission denied. Will try to remount.")
                except Exception as e:
                    # Any other error, try to mount anyway
                    print(f"Mount point check error: {str(e)}. Will try to remount.")
            # Create the mount point directory if it doesn't exist
            if not os.path.exists(mount_point):
                os.makedirs(mount_point, exist_ok=True)
                print(f"Created mount point directory: {mount_point}")
            # Try multiple mounting methods in sequence
            print(f"Attempting to mount {smb_url} at {mount_point}...")
            # Method 1: Using AppleScript with guest credentials
            try:
                applescript = f'''
                tell application "Finder"
                    try
                        mount volume "{smb_url}"
                        return true
                    on error
                        return false
                    end try
                end tell
                '''
                print("Trying AppleScript mount method...")
                result = subprocess.run(['osascript', '-e', applescript],
                                      capture_output=True, text=True, timeout=15)
                if "true" in result.stdout.lower():
                    print(f"Connected to network share successfully using AppleScript: {smb_url}")
                    return True
                else:
                    print(f"AppleScript mount returned: {result.stdout}")
            except Exception as e:
                print(f"AppleScript mount method failed: {str(e)}")
            # Method 2: Using mount_smbfs command
            try:
                # Unmount first if it exists but is not working
                if os.path.exists(mount_point):
                    try:
                        print(f"Unmounting existing mount point: {mount_point}")
                        subprocess.run(f"umount {mount_point}", shell=True, check=False, timeout=5)
                    except Exception as e:
                        print(f"Unmount error (non-critical): {str(e)}")
                # Try mounting with guest credentials
                print("Trying mount_smbfs method...")
                mount_cmd = f"mount_smbfs //guest@{server_address}/{share_name} {mount_point}"
                subprocess.run(mount_cmd, shell=True, check=True, timeout=10)
                # Verify the mount was successful
                os.listdir(mount_point)  # This will raise an exception if not mounted
                print(f"Connected to network share successfully using mount_smbfs: {smb_url}")
                return True
            except Exception as e:
                print(f"mount_smbfs method failed: {str(e)}")
            # Method 3: Using mount command
            try:
                print("Trying mount -t smbfs method...")
                mount_cmd = f"mount -t smbfs //guest@{server_address}/{share_name} {mount_point}"
                subprocess.run(mount_cmd, shell=True, check=True, timeout=10)
                # Verify the mount was successful
                os.listdir(mount_point)  # This will raise an exception if not mounted
                print(f"Connected to network share successfully using mount command: {smb_url}")
                return True
            except Exception as e:
                print(f"mount command method failed: {str(e)}")
            # Method 4: Using open command as a last resort
            try:
                print("Trying open command method...")
                open_cmd = f"open {smb_url}"
                subprocess.run(open_cmd, shell=True, check=True, timeout=10)
                # Wait a moment for the mount to complete
                time.sleep(2)
                # Verify the mount was successful
                if os.path.exists(mount_point) and os.path.isdir(mount_point):
                    try:
                        os.listdir(mount_point)
                        print(f"Connected to network share successfully using open command: {smb_url}")
                        return True
                    except:
                        pass
                print("Open command executed but mount verification failed")
            except Exception as e:
                print(f"open command method failed: {str(e)}")
            # If we got here, all methods failed
            print(f"All network share mounting methods failed for {smb_url}.")
            return False
        except Exception as e:
            print(f"Could not connect to network share: {str(e)}")
            return False
    def load_icon(self, path, size):
        """Load an icon from the specified path and resize it to the given size"""
        try:
            if os.path.exists(path):
                # Open the image file
                img = Image.open(path)
                # Create a CTkImage with the same image for both light and dark modes
                return ctk.CTkImage(light_image=img, dark_image=img, size=size)
            else:
                logging.error(f"Icon not found: {path}")
                # Try a generic document icon as fallback
                fallback_path = get_resource_path("img/vs_icns/vsmactool.icns")
                if os.path.exists(fallback_path):
                    img = Image.open(fallback_path)
                    return ctk.CTkImage(light_image=img, dark_image=img, size=size)
                else:
                    logging.error(f"Fallback icon not found: {fallback_path}")
                    return None
        except Exception as e:
            logging.error(f"Error loading icon {path}: {str(e)}")
            return None
    def update_datetime(self):
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.datetime_label.configure(text=current_time)
        self.after(1000, self.update_datetime)  # Update every second
    def check_internet_connection(self):
        try:
            # Attempt to connect to a reliable server (Google's DNS)
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            return True
        except OSError:
            return False
    def check_internet_periodically(self):
        self.internet_connected = self.check_internet_connection()
        if self.internet_connected:
            self.status_indicator.itemconfig(self.status_light, fill="#34C759")
            self.status_label.configure(text="Internet connection active")
        else:
            self.status_indicator.itemconfig(self.status_light, fill="#FF3B30")
            self.status_label.configure(text="No internet connection")
        self.after(5000, self.check_internet_periodically)  # Check every 5 seconds
    def blink_internet_status(self):
        current_color = self.status_indicator.itemcget(self.status_light, "fill")
        if self.blink_status:
            bg_color = "#2A2A2A"
            active_color = "#34C759" if self.internet_connected else "#FF3B30"
            new_color = bg_color if current_color != bg_color else active_color
            self.status_indicator.itemconfig(self.status_light, fill=new_color)
        self.after(500, self.blink_internet_status)  # Blink every 500ms
    def check_internet(self):
        if not self.internet_connected:
            return "No internet connection."
        try:
            subprocess.check_output(["ping", "-c", "1", "-W", "3", "8.8.8.8"])
            return "Internet connection is active."
        except subprocess.CalledProcessError:
            return "No internet connection."
        except subprocess.TimeoutExpired:
            return "Internet connection check timed out."
    def check_apple_time(self):
        """Check time with Apple's time server"""
        if not self.internet_connected:
            return "No internet connection. Cannot check Apple time server."
        try:
            # Run sntp command to get time from Apple's time server - we don't need the output
            subprocess.check_output("sntp time.apple.com", shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)
            # Parse the output to get the current time
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            return f"Current time: {current_time}"
        except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
            return f"Error getting time from Apple server: {str(e)}"
    def check_apple_server_connection(self):
        """Check connection to Apple servers using network utility"""
        if not self.internet_connected:
            return "No internet connection. Cannot check Apple server connection."
        # Apple server IPs and domains to check
        apple_servers = [
            "*************",  # Apple's main server
            "************",   # Apple's iCloud server
            "swscan.apple.com",
            "www.apple.com"
        ]
        results = []
        # Check for established connections to Apple servers
        for server in apple_servers:
            try:
                # Try to establish a connection
                if "." in server:  # It's an IP address or domain
                    try:
                        socket.create_connection((server, 443), timeout=3)
                        results.append(f"Connection to Apple server {server} - Success")
                    except:
                        results.append(f"Connection to Apple server {server} - Failed")
                else:
                    results.append(f"Skipping invalid server address: {server}")
            except Exception as e:
                results.append(f"Error checking {server}: {str(e)}")
        return "\n".join(results)
    def run_sudo_command(self, command, timeout=10):
        global SUDO_PASSWORD
        logging.info(f"Running sudo command: {command}")
        # Try running the command with the current password
        full_command = f"echo {SUDO_PASSWORD} | sudo -S {command}"
        result = self.run_command(full_command, timeout=timeout)
        # Remove 'Password:' prompt if present
        if result and result.strip().startswith("Password:"):
            result = result.replace("Password:", "").strip()
        # Check if the password was incorrect
        if ("is not in the sudoers file" in result or "incorrect password" in result or "Sorry, try again." in result):
            from tkinter import messagebox
            messagebox.showwarning(
                "Sudo Password Required",
                "Default sudo authentication failed.\nPlease enter your sudo password."
            )
            password_result = get_sudo_password(
                self,
                title="Sudo Password Required",
                message="Authentication failed with default password.\nPlease enter your sudo password:"
            )
            if password_result["cancelled"]:
                return "Operation cancelled by user."
            SUDO_PASSWORD = password_result["password"]
            full_command = f"echo {SUDO_PASSWORD} | sudo -S {command}"
            result = self.run_command(full_command, timeout=timeout)
            if result and result.strip().startswith("Password:"):
                result = result.replace("Password:", "").strip()
        return result
    def run_command(self, command, timeout=10):
        try:
            logging.info(f"Running command: {command}")
            output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.STDOUT, timeout=timeout)
            # Remove 'Password:' prompt if present in output
            lines = output.splitlines()
            filtered_lines = [line for line in lines if not line.strip().startswith("Password:")]
            return "\n".join(filtered_lines).strip()
        except subprocess.CalledProcessError as e:
            logging.error(f"Command failed: {command}\nOutput: {e.output.strip()}")
            # Remove 'Password:' prompt if present in error output
            lines = e.output.splitlines() if e.output else []
            filtered_lines = [line for line in lines if not line.strip().startswith("Password:")]
            return f"Error: {' '.join(filtered_lines).strip()}"
        except subprocess.TimeoutExpired:
            logging.error(f"Command timed out: {command}")
            return "Error: Command timed out"
    def show_device_enrollment_log(self):
        # Further refine the predicate, limit time range to 1 minute, and get last 10 lines to avoid timeout
        command = "log show --predicate 'eventMessage contains \"Device Enrollment\" AND NOT subsystem CONTAINS \"com.apple.SafariServices\"' --info --last 1m --style compact | tail -n 10 | grep -E \"Error|MDM\""
        return self.run_sudo_command(command, timeout=300)
    def open_system_preferences(self):
        os.system("open /System/Library/PreferencePanes/Profiles.prefPane")
        return "Opened relevant System Settings sections. Please check the Profiles pane."
    def exit_app(self):
        self.quit()
    def shutdown_system(self):
        """Show shutdown confirmation dialog"""
        command = "osascript -e 'tell app \"System Events\" to display dialog \"Are you sure you want to shut down the system?\" buttons {\"Cancel\", \"Shut Down\"} default button \"Cancel\" with icon caution' -e 'if button returned of result is \"Shut Down\" then tell app \"System Events\" to shut down'"
        self.run_sudo_command(command)
        return "Shutdown dialog displayed"
    def auto_execute(self):
        # Reset all checkboxes to unchecked state
        for checkbox in self.checkboxes:
            self.after(0, checkbox.deselect) # Use self.after to run in main thread
        # Clear the process logs textbox
        self.after(0, lambda: self.result_text.configure(state="normal"))
        self.after(0, lambda: self.result_text.delete("1.0", ctk.END))
        self.after(0, lambda: self.result_text.configure(state="disabled"))

        # Reset any previous highlighting
        self.current_highlighted_checkbox_idx = -1

        threading.Thread(target=self._auto_execute_thread, daemon=True).start()
    def _auto_execute_thread(self):
        for i, (text, command) in enumerate(self.buttons):
            # Un-highlight previous step and highlight current step
            if self.current_highlighted_checkbox_idx != -1:
                self.after(0, lambda idx=self.current_highlighted_checkbox_idx: self.checkboxes[idx].configure(text_color="#FFFFFF"))
            self.current_highlighted_checkbox_idx = i
            self.after(0, lambda idx=i: self.checkboxes[idx].configure(text_color="#FFD700")) # Highlight current step in gold

            try:
                if not self.internet_connected and text not in ["Check Internet", "Open Profiles"]:
                    result = "Skipped due to no internet connection"
                elif callable(command):
                    result = command()
                else:
                    result = self.run_command(command)
                self.result_queue.put(f"{text}:\n{result}")
                self.after(0, lambda idx=i: self.checkboxes[idx].select())
            except Exception as e:
                self.result_queue.put(f"Error executing {text}: {str(e)}")
            finally:
                # Ensure un-highlighting and checking happens even if there's an error
                self.after(0, lambda idx=i: self.checkboxes[idx].configure(text_color="#FFFFFF")) # Revert color after completion
            time.sleep(1)  # Add a small delay between steps

        # After all steps are done, clear any remaining highlight
        if self.current_highlighted_checkbox_idx != -1:
            self.after(0, lambda idx=self.current_highlighted_checkbox_idx: self.checkboxes[idx].configure(text_color="#FFFFFF"))
            self.current_highlighted_checkbox_idx = -1
    def display_result(self, result):
        self.result_text.configure(state="normal")
        # Define tags for different types of messages using macOS colors
        self.result_text.tag_config("success", foreground="#34C759")
        self.result_text.tag_config("error", foreground="#FF3B30")
        self.result_text.tag_config("info", foreground="#C3679E")
        self.result_text.tag_config("result", foreground="#007AFF")
        self.result_text.tag_config("dep_enrolled", foreground="#FF3B30")  # RED
        self.result_text.tag_config("dep_not_enrolled", foreground="#007AFF")  # BLUE
        self.result_text.tag_config("bt_not_connected", foreground="#007AFF")  # BLUE for Not Connected
        self.result_text.tag_config("ade_company", foreground="#FFD700")  # Gold highlight for ADE/DEP company
        self.result_text.tag_config("gold_title", foreground="#FFD700")  # Gold for check titles
        # Split the result into lines
        lines = result.split('\n')
        # Filter out sudo password prompts from logs
        lines = [line for line in lines if not line.strip().startswith("Password:")]
        # List of security lock check titles to highlight
        gold_titles = [
            "Check Time on time.apple.com:",
            "Check Apple Server Connection:",
            "Check ADE/DEP Enrollment:",
            "Raw status output:",
            "Check MDM Enrollment Status:",
            "Check Installed Profiles:",
            "Show Device Enrollment Log:",
            "Current Bluetooth devices:",
            "Remove Paired Bluetooth Devices:"
        ]
        # First line is usually the command/check name
        if lines and ":" in lines[0]:
            command_parts = lines[0].split(":", 1)
            # Highlight the title if it matches
            full_title = command_parts[0] + ":"
            if full_title in gold_titles:
                self.result_text.insert(ctk.END, full_title + "\n", "gold_title")
            else:
                self.result_text.insert(ctk.END, full_title + "\n")
            if len(lines) > 1:
                is_dep_enrollment_check = "Check ADE/DEP Enrollment:" in lines[0]
                is_mdm_enrollment_check = "Check MDM Enrollment Status:" in lines[0]
                is_bt_devices_check = "Current Bluetooth devices:" in lines[0]
                in_raw_status = False
                for line in lines[1:]:
                    # Highlight '<company> can automatically configure your Mac' in gold
                    import re
                    ade_company_match = re.search(r"([\w\s]+) can automatically configure your Mac", line)
                    if ade_company_match:
                        self.result_text.insert(ctk.END, line + "\n", "ade_company")
                        continue
                    # For Check ADE/DEP Enrollment, highlight only the result part in summary and raw status output
                    if is_dep_enrollment_check and ("Enrollment Status:" in line or "DEP Profile:" in line):
                        import re
                        match = re.search(r'(ENROLLED|NOT ENROLLED|INSTALLED|NOT INSTALLED)', line)
                        if match:
                            start, end = match.span()
                            self.result_text.insert(ctk.END, line[:start])
                            tag = "dep_enrolled" if match.group() in ["ENROLLED", "INSTALLED"] and "NOT" not in match.group() else "dep_not_enrolled"
                            self.result_text.insert(ctk.END, match.group(), tag)
                            self.result_text.insert(ctk.END, line[end:] + "\n")
                        else:
                            self.result_text.insert(ctk.END, line + "\n")
                    elif is_dep_enrollment_check and (line.strip().startswith("Raw status output:")):
                        in_raw_status = True
                        self.result_text.insert(ctk.END, line + "\n", "gold_title")
                    elif is_dep_enrollment_check and in_raw_status and (":" in line):
                        # Highlight only the result (No/Yes) in raw status output
                        import re
                        match = re.search(r'\b(Yes|No)\b', line)
                        if match:
                            start, end = match.span()
                            self.result_text.insert(ctk.END, line[:start])
                            tag = "dep_enrolled" if match.group() == "Yes" else "dep_not_enrolled"
                            self.result_text.insert(ctk.END, match.group(), tag)
                            self.result_text.insert(ctk.END, line[end:] + "\n")
                        else:
                            self.result_text.insert(ctk.END, line + "\n")
                    elif is_mdm_enrollment_check and (":" in line):
                        # Highlight only the result (No/Yes) in MDM status output
                        import re
                        match = re.search(r'\b(Yes|No)\b', line)
                        if match:
                            start, end = match.span()
                            self.result_text.insert(ctk.END, line[:start])
                            tag = "dep_enrolled" if match.group() == "Yes" else "dep_not_enrolled"
                            self.result_text.insert(ctk.END, match.group(), tag)
                            self.result_text.insert(ctk.END, line[end:] + "\n")
                        else:
                            self.result_text.insert(ctk.END, line + "\n")
                    elif is_bt_devices_check and "Not Connected" in line:
                        idx = line.lower().find("not connected")
                        if idx != -1:
                            self.result_text.insert(ctk.END, line[:idx])
                            self.result_text.insert(ctk.END, line[idx:idx+13], "bt_not_connected")
                            self.result_text.insert(ctk.END, line[idx+13:] + "\n")
                            continue
                    else:
                        # Restore original highlighting logic for all other lines
                        is_device_enrollment_log = "Show Device Enrollment Log:" in lines[0]
                        is_apple_time_check = "Check Time on time.apple.com:" in lines[0]
                        # Highlight success messages in green
                        if "Success" in line or ("Connection to Apple server" in line and "Success" in line):
                            self.result_text.insert(ctk.END, line + "\n", "success")
                        # Special handling for Device Enrollment Log entries
                        elif is_device_enrollment_log and "[com.apple.log]" in line and "noninteractively" in line:
                            self.result_text.insert(ctk.END, line + "\n", "success")
                        # Highlight error messages in red
                        elif "Error" in line or "Failed" in line or "No internet connection" in line:
                            self.result_text.insert(ctk.END, line + "\n", "error")
                        # Highlight specific result lines in blue
                        elif (line.startswith("DEP Status:") or
                              line.strip() == "No" or
                              "No profiles installed" in line or
                              "Enrolled via DEP:" in line or
                              "MDM enrollment:" in line or
                              line.strip() == "None" or
                              (is_apple_time_check and line.startswith("Current time"))):
                            self.result_text.insert(ctk.END, line + "\n", "result")
                        # Special handling for Device Enrollment Log with specific text highlighting
                        elif "No Device Enrollment configuration was found for this computer" in line:
                            parts = line.split("No Device Enrollment configuration was found for this computer")
                            self.result_text.insert(ctk.END, parts[0])
                            self.result_text.insert(ctk.END, "No Device Enrollment configuration was found for this computer", "result")
                            if len(parts) > 1:
                                self.result_text.insert(ctk.END, parts[1] + "\n")
                            else:
                                self.result_text.insert(ctk.END, "\n")
                        # Other informational messages in yellow
                        elif "Please" in line or "ensure" in line or "If you've recently" in line:
                            self.result_text.insert(ctk.END, line + "\n", "info")
                        # Everything else in normal text
                        else:
                            self.result_text.insert(ctk.END, line + "\n")
            elif len(command_parts) > 1:
                self.result_text.insert(ctk.END, command_parts[1] + "\n", "result")
        else:
            for line in lines:
                self.result_text.insert(ctk.END, line + "\n")
        self.result_text.insert(ctk.END, "\n")
        self.result_text.configure(state="disabled")
        self.result_text.see(ctk.END)
        self.update_idletasks()  # Force GUI update
    def check_result_queue(self):
        try:
            while True:
                result = self.result_queue.get_nowait()
                self.after(0, lambda r=result: self.display_result(r))
        except queue.Empty:
            pass
        finally:
            self.after(100, self.check_result_queue)
    def check_activation_lock_status(self):
        command = "system_profiler SPHardwareDataType | grep 'Activation Lock Status'"
        try:
            output = subprocess.check_output(command, shell=True, text=True)
            return f"Activation Lock Status:\n{output.strip()}"
        except subprocess.CalledProcessError:
            return "Failed to check Activation Lock Status"
    def update_hardware_info(self):
        info = self.get_hardware_info()
        # Map 'Activation Lock Status' to 'Activation Lock' for display
        if 'Activation Lock' in self.hardware_info_entries and 'Activation Lock Status' in info:
            info['Activation Lock'] = info['Activation Lock Status']
        for key, entry in self.hardware_info_entries.items():
            value = info.get(key, "")
            entry.configure(state="normal")
            entry.delete(0, ctk.END)
            entry.insert(0, value)
            # Highlight Activation Lock: green if Disabled, red if Enabled, gray otherwise
            if key == "Activation Lock":
                if value.strip().lower() == "enabled":
                    entry.configure(text_color="#db5462")  # Red
                elif value.strip().lower() == "disabled":
                    entry.configure(text_color="#34C759")  # Green
                else:
                    entry.configure(text_color="#B3B3B3")  # Gray
                entry.configure(font=("SF Pro", 12, "bold"))
            else:
                entry.configure(text_color="#FFFFFF", font=("SF Pro", 11))
            entry.configure(state="readonly")
        # Force update of the GUI
        self.update_idletasks()
    def get_hardware_info(self):
        commands = {
            "Model Identifier": "sysctl -n hw.model",
            "Chip": "sysctl -n machdep.cpu.brand_string",
            "Memory": "sysctl -n hw.memsize | awk '{ printf \"%.0f GB\", $0/1024/1024/1024 }'",
            "SSD Storage": "diskutil info / | grep 'Disk Size' | awk '{size=$3; if (size < 64) print \"64GB\"; else if (size < 128) print \"128GB\"; else if (size < 256) print \"256GB\"; else if (size < 512) print \"512GB\"; else if (size < 1024) print \"1TB\"; else print \"2TB\"}'",
            "Serial number": "ioreg -c IOPlatformExpertDevice -d 2 | grep -i 'IOPlatformSerialNumber' | awk -F '\"' '{print $4}'",
            "macOS Version": "sw_vers -productVersion",
            "Activation Lock Status": "system_profiler SPHardwareDataType | grep -i 'Activation Lock' | awk -F ': ' '{print $2}'"
        }
        info = {}
        for key, command in commands.items():
            try:
                output = subprocess.check_output(command, shell=True, text=True).strip()
                info[key] = output.strip('"')  # Remove any quotation marks
            except subprocess.CalledProcessError as e:
                info[key] = "Unable to retrieve"
        return info
    def check_dep_enrollment(self):
        """Check DEP/ADE enrollment status accurately, and cross-check with device enrollment logs for ADE/DEP eligibility."""
        import time
        import re
        try:
            # Step 1: Force check-in
            renew_result = self.run_sudo_command("sudo profiles renew -type enrollment")
            # Step 2: Wait a few seconds for the system to process
            time.sleep(5)
            # Step 3: Check status
            status_result = self.run_sudo_command("sudo profiles status -type enrollment")
            # Step 4: Check for .cloudConfigProfileInstalled file
            cloud_config_path = "/var/db/ConfigurationProfiles/Settings/.cloudConfigProfileInstalled"
            cloud_config_installed = os.path.exists(cloud_config_path)
            # Step 5: Parse and summarize
            dep_status = "Unknown"
            mdm_status = "Unknown"
            for line in status_result.splitlines():
                if "Enrolled via DEP:" in line:
                    dep_status = line.split(":",1)[1].strip()
                if "MDM enrollment:" in line:
                    mdm_status = line.split(":",1)[1].strip()
            # Cross-check device enrollment log for ADE/DEP eligibility
            log_result = self.show_device_enrollment_log()
            # Look for 'can automatically configure your Mac' and extract company name
            ade_company_match = re.search(r"([\w\s]+) can automatically configure your Mac", log_result)
            ade_company = ade_company_match.group(1).strip() if ade_company_match else None
            summary = []
            summary.append(f"DEP (ADE) Enrollment Status: {'ENROLLED' if dep_status == 'Yes' else 'NOT ENROLLED'}")
            summary.append(f"MDM Enrollment Status: {'ENROLLED' if mdm_status == 'Yes' else 'NOT ENROLLED'}")
            if cloud_config_installed:
                summary.append("DEP Profile: INSTALLED (.cloudConfigProfileInstalled present)")
            else:
                summary.append("DEP Profile: NOT INSTALLED (.cloudConfigProfileInstalled missing)")
            # Add note if eligible but not enrolled
            if ade_company and dep_status != 'Yes':
                summary.append(f"NOTE: Device is eligible for ADE/DEP (per logs: '{ade_company} can automatically configure your Mac'), but not currently enrolled.")
            summary.append("")
            summary.append("Raw status output:")
            summary.append(status_result.strip())
            return "\n".join(summary)
        except Exception as e:
            return f"Error checking DEP enrollment: {str(e)}"
    def open_findmy(self):
        """Open FindMy application"""
        try:
            # Try different possible names for the FindMy app
            for app_name in ["FindMy", "Find My", "FindMy.app", "Find My Mac"]:
                result = os.system(f"open -a '{app_name}'")
                if result == 0:  # Command succeeded
                    return f"Opened {app_name} application."
            # If none of the above worked, try opening it via URL scheme
            os.system("open x-apple.findmy://")
            return "Attempted to open FindMy application via URL scheme."
        except Exception as e:
            return f"Error opening FindMy: {str(e)}"
    def save_hardware_overview(self):
        """Save hardware information to CSV file"""
        info = self.get_hardware_info()
        mount_point = self.network_config["mount_point"]
        server_address = self.network_config["server"]
        share_name = self.network_config["share"]
        smb_url = f"smb://{server_address}/{share_name}"
        # Function to mount the network share
        def mount_network_share():
            try:
                if os.path.exists(mount_point) and os.path.isdir(mount_point):
                    try:
                        os.listdir(mount_point)
                        return True
                    except Exception:
                        pass
                # Prompt user to connect
                from tkinter import messagebox
                answer = messagebox.askyesno("Connect to Network Share", f"The network share {smb_url} is not connected. Connect now?")
                if not answer:
                    return False
                # Try AppleScript
                applescript = f'''
                tell application "Finder"
                    try
                        mount volume "{smb_url}"
                        return true
                    on error
                        return false
                    end try
                end tell
                '''
                result = subprocess.run(['osascript', '-e', applescript], capture_output=True, text=True, timeout=15)
                if "true" in result.stdout.lower():
                    return True
                # Try mount_smbfs
                mount_cmd = f"mount_smbfs //guest@{server_address}/{share_name} {mount_point}"
                subprocess.run(mount_cmd, shell=True, check=True, timeout=10)
                os.listdir(mount_point)
                return True
            except Exception as e:
                self.display_result(f"Could not connect to network share: {str(e)}")
                return False
        # Try to mount the share
        mount_successful = mount_network_share()
        if mount_successful and os.path.exists(mount_point) and os.path.isdir(mount_point):
            initialdir = mount_point
            self.display_result(f"Using network share {smb_url} for saving files.")
        else:
            self.display_result("Network share not available. Save cancelled.")
            return
        current_date = datetime.now().strftime("%Y%m%d")
        suggested_filename = f"System_Info_{current_date}.csv"
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv")],
            initialdir=initialdir,
            initialfile=suggested_filename
        )
        if file_path:
            with open(file_path, 'w', newline='') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(["Property", "Value"])
                for key, value in info.items():
                    writer.writerow([key, value])
            self.display_result(f"System Information saved to {file_path}")
        else:
            self.display_result("Save operation cancelled")
    def create_xml_info_dialog(self):
        """Create a dialog to collect additional information for XML export"""
        # Create a new dialog window
        dialog = ctk.CTkToplevel(self)
        dialog.title("XML Custom Field Information")
        dialog.geometry("400x330")  # Initial size
        dialog.resizable(False,False)
        dialog.grab_set()  # Make the dialog modal
        # Center the dialog over the main window
        self.update_idletasks()  # Ensure main window geometry is up to date
        main_x = self.winfo_rootx()
        main_y = self.winfo_rooty()
        main_w = self.winfo_width()
        main_h = self.winfo_height()
        dialog_w = 400
        dialog_h = 330
        pos_x = main_x + (main_w - dialog_w) // 2
        pos_y = main_y + (main_h - dialog_h) // 2
        dialog.geometry(f"{dialog_w}x{dialog_h}+{pos_x}+{pos_y}")
        # Set dark theme
        dialog.configure(fg_color="#1E1E1E")
        # Create form fields
        ctk.CTkLabel(dialog, text="Please enter the following information:", font=("SF Pro", 14, "bold"), text_color="#FFFFFF").pack(pady=(5, 0))
        # Add note about required fields
        # Create a frame for the required note to hold multiple elements
        required_note_frame = ctk.CTkFrame(dialog, fg_color="#1E1E1E")
        required_note_frame.pack(pady=(0, 5))
        # Add the text in parts to allow different colors
        ctk.CTkLabel(required_note_frame, text="Fields marked with ", font=("SF Pro", 12), text_color="#B3B3B3").pack(side="left")
        ctk.CTkLabel(required_note_frame, text="*", font=("SF Pro", 12, "bold"), text_color="#CD5C5C").pack(side="left")
        ctk.CTkLabel(required_note_frame, text=" are required", font=("SF Pro", 12), text_color="#B3B3B3").pack(side="left")
        # Create a frame for the form fields - adjust the bottom padding
        form_frame = ctk.CTkFrame(dialog, fg_color="#1E1E1E")
        form_frame.pack(fill="both", expand=False, padx=20, pady=(2, 0))  # Changed expand to False and bottom padding to 0
        # Configure columns for the form frame
        form_frame.grid_columnconfigure(0, weight=0)  # Label column
        form_frame.grid_columnconfigure(1, weight=0)  # Asterisk column
        form_frame.grid_columnconfigure(2, weight=1)  # Entry column
        form_frame.grid_columnconfigure(3, weight=0)  # Error message column
        # Create validation message labels (initially empty)
        tp_validation = ctk.CTkLabel(form_frame, text="", text_color="#CD5C5C", font=("SF Pro", 11))
        load_validation = ctk.CTkLabel(form_frame, text="", text_color="#CD5C5C", font=("SF Pro", 11))
        usertag_validation = ctk.CTkLabel(form_frame, text="", text_color="#CD5C5C", font=("SF Pro", 11))
        # TP Number field with required asterisk
        ctk.CTkLabel(form_frame, text="TP Number:", anchor="w", text_color="#FFFFFF").grid(row=0, column=0, padx=(10, 2), pady=5, sticky="w")
        ctk.CTkLabel(form_frame, text="*", text_color="#CD5C5C", font=("SF Pro", 14, "bold")).grid(row=0, column=1, padx=(0, 5), pady=5, sticky="w")
        tp_number_var = ctk.StringVar()
        tp_entry = ctk.CTkEntry(form_frame, textvariable=tp_number_var, width=200, text_color="#FFFFFF")
        tp_entry.grid(row=0, column=2, padx=5, pady=5, sticky="w")
        tp_validation.grid(row=0, column=3, padx=5, pady=5, sticky="w")
        # Load Number field with required asterisk
        ctk.CTkLabel(form_frame, text="Load number:", anchor="w", text_color="#FFFFFF").grid(row=1, column=0, padx=(10, 2), pady=5, sticky="w")
        ctk.CTkLabel(form_frame, text="*", text_color="#CD5C5C", font=("SF Pro", 14, "bold")).grid(row=1, column=1, padx=(0, 5), pady=5, sticky="w")
        load_number_var = ctk.StringVar()
        load_entry = ctk.CTkEntry(form_frame, textvariable=load_number_var, width=200, text_color="#FFFFFF")
        load_entry.grid(row=1, column=2, padx=5, pady=5, sticky="w")
        load_validation.grid(row=1, column=3, padx=5, pady=5, sticky="w")
        # Usertag field with required asterisk
        ctk.CTkLabel(form_frame, text="Usertag:", anchor="w", text_color="#FFFFFF").grid(row=2, column=0, padx=(10, 2), pady=5, sticky="w")
        ctk.CTkLabel(form_frame, text="*", text_color="#CD5C5C", font=("SF Pro", 14, "bold")).grid(row=2, column=1, padx=(0, 5), pady=5, sticky="w")
        current_user = os.getlogin()
        default_usertag = "" if current_user == "root" else current_user
        usertag_var = ctk.StringVar(value=default_usertag)
        usertag_entry = ctk.CTkEntry(form_frame, textvariable=usertag_var, width=200, text_color="#FFFFFF")
        usertag_entry.grid(row=2, column=2, padx=5, pady=5, sticky="w")
        usertag_validation.grid(row=2, column=3, padx=5, pady=5, sticky="w")
        # Client Asset Number field (not required)
        ctk.CTkLabel(form_frame, text="Client Asset Number:", anchor="w", text_color="#FFFFFF").grid(row=3, column=0, padx=(10, 2), pady=5, sticky="w")
        ctk.CTkLabel(form_frame, text="", font=("SF Pro", 14), text_color="#FFFFFF").grid(row=3, column=1, padx=(0, 5), pady=5, sticky="w")
        asset_number_var = ctk.StringVar()
        asset_entry = ctk.CTkEntry(form_frame, textvariable=asset_number_var, width=200, text_color="#FFFFFF")
        asset_entry.grid(row=3, column=2, padx=5, pady=5, sticky="w")
        # Comment field (not required)
        ctk.CTkLabel(form_frame, text="Comment:", anchor="w", text_color="#FFFFFF").grid(row=4, column=0, padx=(10, 2), pady=5, sticky="w")
        ctk.CTkLabel(form_frame, text="", font=("SF Pro", 14), text_color="#FFFFFF").grid(row=4, column=1, padx=(0, 5), pady=10, sticky="w")
        comment_var = ctk.StringVar()
        comment_entry = ctk.CTkEntry(form_frame, textvariable=comment_var, width=200, text_color="#FFFFFF")
        comment_entry.grid(row=4, column=2, padx=5, pady=10, sticky="w")
        # Create a frame for buttons with minimal padding
        button_frame = ctk.CTkFrame(dialog, fg_color="#1E1E1E")
        button_frame.pack(fill="x", padx=20, pady=(10, 5))  # Top padding set to 0
        # Dictionary to store the results
        result = {"confirmed": False, "data": {}}
        # Function to clear all validation messages
        def clear_validation_messages():
            tp_validation.configure(text="")
            load_validation.configure(text="")
            usertag_validation.configure(text="")
        # Function to handle OK button click
        def on_ok():
            # Clear any previous validation messages
            clear_validation_messages()
            # Check if required fields are filled
            tp_number = tp_number_var.get().strip()
            load_number = load_number_var.get().strip()
            usertag = usertag_var.get().strip()
            # Flag to track if validation passed
            validation_passed = True
            if not tp_number:
                tp_validation.configure(text="Required")
                tp_entry.focus_set()
                validation_passed = False
            if not load_number:
                load_validation.configure(text="Required")
                if validation_passed:  # Only set focus if no previous field had an error
                    load_entry.focus_set()
                validation_passed = False
            if not usertag:
                usertag_validation.configure(text="Required")
                if validation_passed:  # Only set focus if no previous field had an error
                    usertag_entry.focus_set()
                validation_passed = False
            # If validation failed, return without closing the dialog
            if not validation_passed:
                return
            # All validation passed, save the data and close the dialog
            result["confirmed"] = True
            result["data"] = {
                "tp_number": tp_number,
                "load_number": load_number,
                "usertag": usertag,
                "asset_number": asset_number_var.get().strip(),
                "comment": comment_var.get().strip()
            }
            dialog.destroy()
        # Function to handle Cancel button click
        def on_cancel():
            dialog.destroy()
        # Add OK and Cancel buttons
        ctk.CTkButton(button_frame, text="OK", command=on_ok,
                     fg_color="#007ACC", hover_color="#005999").pack(side="right", padx=20)
        ctk.CTkButton(button_frame, text="Cancel", command=on_cancel,
                     fg_color="#333333", hover_color="#444444").pack(side="right", padx=5)
        # Set focus to the first field
        tp_entry.focus_set()
        # Wait for the dialog to be closed
        self.wait_window(dialog)
        return result
    def save_hardware_to_xml(self):
        """Save hardware information to XML file compatible with Blancco Management Console"""
        dialog_result = self.create_xml_info_dialog()
        if not dialog_result["confirmed"]:
            self.display_result("XML export cancelled")
            return
        form_data = dialog_result["data"]
        tp_number = form_data["tp_number"]
        load_number = form_data["load_number"]
        usertag = form_data["usertag"]
        asset_number = form_data["asset_number"]
        comment = form_data["comment"]
        document_id = f"{tp_number}@{usertag}"
        info = self.get_hardware_info()
        mount_point = self.network_config["mount_point"]
        server_address = self.network_config["server"]
        share_name = self.network_config["share"]
        smb_url = f"smb://{server_address}/{share_name}"
        def mount_network_share():
            try:
                if os.path.exists(mount_point) and os.path.isdir(mount_point):
                    try:
                        os.listdir(mount_point)
                        return True
                    except Exception:
                        pass
                from tkinter import messagebox
                answer = messagebox.askyesno("Connect to Network Share", f"The network share {smb_url} is not connected. Connect now?")
                if not answer:
                    return False
                applescript = f'''
                tell application "Finder"
                    try
                        mount volume "{smb_url}"
                        return true
                    on error
                        return false
                    end try
                end tell
                '''
                result = subprocess.run(['osascript', '-e', applescript], capture_output=True, text=True, timeout=15)
                if "true" in result.stdout.lower():
                    return True
                mount_cmd = f"mount_smbfs //guest@{server_address}/{share_name} {mount_point}"
                subprocess.run(mount_cmd, shell=True, check=True, timeout=10)
                os.listdir(mount_point)
                return True
            except Exception as e:
                self.display_result(f"Could not connect to network share: {str(e)}")
                return False
        mount_successful = mount_network_share()
        if mount_successful and os.path.exists(mount_point) and os.path.isdir(mount_point):
            initialdir = mount_point
            self.display_result(f"Using network share {smb_url} for saving files.")
        else:
            self.display_result("Network share not available. Save cancelled.")
            return
        suggested_filename = f"{tp_number}.xml" if tp_number else (f"{load_number}.xml" if load_number else "export.xml")
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xml",
            filetypes=[("XML files", "*.xml")],
            initialfile=suggested_filename,
            initialdir=initialdir
        )
        if not file_path:
            self.display_result("Save operation cancelled")
            return
        # Create XML structure based on blancco_report.xml format (working format)
        root = ET.Element("root")
        report = ET.SubElement(root, "report")
        # Create blancco_data section
        blancco_data = ET.SubElement(report, "blancco_data")
        # Create description section
        description = ET.SubElement(blancco_data, "description")
        ET.SubElement(description, "document_id").text = document_id
        # Create document_log section
        document_log = ET.SubElement(description, "document_log")
        # Add log entry
        log_entry = ET.SubElement(document_log, "log_entry")
        author = ET.SubElement(log_entry, "author")
        product_name = ET.SubElement(author, "product_name", id="51", name="VS Mac Security Checker")
        product_name.text = "Blancco Management Console"
        ET.SubElement(author, "product_version").text = "2.2.1"
        ET.SubElement(author, "product_revision").text = "N/A"
        # Add current date/time in ISO format with timezone
        current_time = datetime.now().strftime("%Y-%m-%dT%H:%M:%S+0900")
        ET.SubElement(log_entry, "date").text = current_time
        ET.SubElement(log_entry, "integrity").text = "Not applicable, user created"
        ET.SubElement(log_entry, "key")
        # Add description entries
        description_entries = ET.SubElement(description, "entry", name="description_entries")
        ET.SubElement(description_entries, "entry", name="verified", type="string").text = "false"
        ET.SubElement(description_entries, "entry", name="verified", type="string").text = "false"
        ET.SubElement(description_entries, "entry", name="verified", type="string").text = "true"
        # Create hardware report section
        hardware_report = ET.SubElement(blancco_data, "blancco_hardware_report")
        system_entries = ET.SubElement(hardware_report, "entries", name="system")
        # Add manufacturer entry
        ET.SubElement(system_entries, "entry", name="manufacturer", type="string").text = "Apple Inc."
        # Map our hardware info keys to the XML format keys
        key_mapping = {
            "Model Identifier": "model",
            "Serial number": "serial"
        }
        # Add hardware info entries
        for key, value in info.items():
            if key in key_mapping:
                ET.SubElement(system_entries, "entry", name=key_mapping[key], type="string").text = value
        # Add chassis type
        ET.SubElement(system_entries, "entry", name="chassis_type", type="string").text = "Notebook"
        # Add RAM information (NEW) - using correct format from old_blancco_report.xml
        memory_str = info.get("Memory", "8GB")
        ET.SubElement(system_entries, "entry", name="ram", type="string").text = memory_str
        # Add Processor information (NEW)
        processor_entries = ET.SubElement(hardware_report, "entries", name="processors")
        processor_device = ET.SubElement(processor_entries, "entries", name="processor")
        # Processor details based on chip info
        chip_info = info.get("Chip", "Apple M1")
        # Handle Apple Silicon chips (M1, M2, M3, M4, and future)
        import re
        m_match = re.search(r"M(\d+)", chip_info)
        if m_match:
            m_gen = m_match.group(1)
            ET.SubElement(processor_device, "entry", name="vendor", type="string").text = "Apple Inc."
            # Use the full chip_info string for the model (e.g., 'Apple M1 Pro')
            ET.SubElement(processor_device, "entry", name="model", type="string").text = chip_info
            # Set cores and speed based on generation (update as needed for future chips)
            cores = "8"
            speed_map = {"1": "3200", "2": "3500", "3": "4000", "4": "4200"}
            speed = speed_map.get(m_gen, "4200")  # Default to 4200 for future chips
            ET.SubElement(processor_device, "entry", name="cores", type="uint").text = cores
            ET.SubElement(processor_device, "entry", name="nominal_speed", type="uint").text = speed
        elif "Intel" in chip_info:
            ET.SubElement(processor_device, "entry", name="vendor", type="string").text = "Intel Corporation"
            ET.SubElement(processor_device, "entry", name="model", type="string").text = chip_info
            ET.SubElement(processor_device, "entry", name="cores", type="uint").text = "4"
            ET.SubElement(processor_device, "entry", name="nominal_speed", type="uint").text = "2800"
        else:
            ET.SubElement(processor_device, "entry", name="vendor", type="string").text = "Apple Inc."
            ET.SubElement(processor_device, "entry", name="model", type="string").text = chip_info
            ET.SubElement(processor_device, "entry", name="cores", type="uint").text = "8"
            ET.SubElement(processor_device, "entry", name="nominal_speed", type="uint").text = "4200"
        # Add disk information (existing format)
        disks_entries = ET.SubElement(hardware_report, "entries", name="disks")
        disk_entry = ET.SubElement(disks_entries, "entries", name="disk")
        ET.SubElement(disk_entry, "entry", name="id", type="uint").text = "34"
        ET.SubElement(disk_entry, "entry", name="index", type="uint").text = "1"
        ET.SubElement(disk_entry, "entry", name="model", type="string").text = info.get("Model Identifier", "Unknown")
        ET.SubElement(disk_entry, "entry", name="vendor", type="string").text = "Apple"
        ET.SubElement(disk_entry, "entry", name="serial", type="string").text = info.get("Serial number", "Unknown")
        ET.SubElement(disk_entry, "entry", name="interface_type", type="string").text = "NVMe"
        # Get storage capacity
        storage_str = info.get("SSD Storage", "512GB")
        # Convert storage to bytes (approximate)
        if "TB" in storage_str:
            capacity = int(float(storage_str.replace("TB", "").strip()) * 1000000000000)
        else:
            capacity = int(float(storage_str.replace("GB", "").strip()) * 1000000000)
        ET.SubElement(disk_entry, "entry", name="capacity", type="uint").text = str(capacity)
        # Create user data section (original working format)
        user_data = ET.SubElement(report, "user_data")
        fields = ET.SubElement(user_data, "entries", name="fields")
        ET.SubElement(fields, "entry", name="Load_number", type="string").text = load_number
        ET.SubElement(fields, "entry", name="TP Number", type="string").text = tp_number
        ET.SubElement(fields, "entry", name="Usertag", type="string").text = usertag if usertag else ""
        ET.SubElement(fields, "entry", name="Client_Asset_number", type="string").text = asset_number
        ET.SubElement(fields, "entry", name="Comment", type="string").text = comment
        # Convert to XML string with proper formatting
        xml_str = ET.tostring(root, encoding='utf-8')
        # Parse and prettify the XML
        dom = xml.dom.minidom.parseString(xml_str)
        pretty_xml = dom.toprettyxml(indent="  ")
        # Remove empty lines and fix formatting
        lines = [line for line in pretty_xml.split('\n') if line.strip()]
        formatted_xml = '\n'.join(lines)
        # Write to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(formatted_xml)
        self.display_result(f"System Information saved to XML file: {file_path}")
    def open_bluetooth_settings(self):
        os.system("open /System/Library/PreferencePanes/Bluetooth.prefPane")
    def show_system_info(self):
        os.system("open -a 'System Information'")
    def erase_app(self):
        """Open the Erase Assistant app"""
        # The path to Erase Assistant varies by macOS version
        if get_macos_version_tuple() >= (11, 0, 0):  # Big Sur or later
            command = "open /System/Library/CoreServices/Erase\\ Assistant.app"
        elif get_macos_version_tuple() >= (10, 15, 0):  # Catalina
            command = "open -a 'System Preferences' /System/Library/PreferencePanes/Reset.prefPane"
        else:
            return "Erase Assistant is not available on this version of macOS."
        result = self.run_command(command)
        return f"Opened Erase Assistant: {result}"
    def delete_bluetooth_devices(self):
        """Delete all paired Bluetooth devices"""
        # First, check and display paired devices
        devices_info = self.check_bluetooth_devices()
        self.display_result(f"Current Bluetooth devices:\n{devices_info}")
        # Improved: Remove both system and user-level Bluetooth caches and restart Bluetooth
        commands = [
            "sudo rm -f /Library/Preferences/com.apple.Bluetooth.plist",
            "sudo rm -f ~/Library/Preferences/com.apple.Bluetooth.plist",
            "sudo rm -rf /Library/Preferences/com.apple.Bluetooth/*",
            "sudo rm -rf ~/Library/Preferences/com.apple.Bluetooth/*",
            "sudo defaults delete /Library/Preferences/com.apple.Bluetooth.plist DeviceCache",
            "sudo defaults delete /Library/Preferences/com.apple.Bluetooth.plist PairedDevices",
            "sudo defaults delete ~/Library/Preferences/com.apple.Bluetooth.plist DeviceCache",
            "sudo defaults delete ~/Library/Preferences/com.apple.Bluetooth.plist PairedDevices"
        ]
        results = []
        for cmd in commands:
            try:
                result = self.run_sudo_command(cmd)
                results.append(result)
            except Exception as e:
                results.append(f"Error: {str(e)}")
        # Restart Bluetooth service
        restart_cmds = ["sudo pkill -HUP blued"]
        if get_macos_version_tuple() >= (11, 0, 0):  # Big Sur or newer
            restart_cmds.append("sudo launchctl kickstart -k system/com.apple.blued")
        for restart_cmd in restart_cmds:
            try:
                restart_result = self.run_sudo_command(restart_cmd)
                results.append(restart_result)
            except Exception as e:
                results.append(f"Error restarting Bluetooth: {str(e)}")
        # Return a summary of the results
        return ("Deleted all registered Bluetooth devices. Please restart your computer to complete the process. "
                "If devices remain, remove manually in System Settings > Bluetooth.")
    def check_findmy_status(self):
        """Check if FindMy is enabled on the device"""
        try:
            # First check if FindMy is enabled via iCloud
            command = "defaults read ~/Library/Preferences/MobileMeAccounts.plist Accounts | grep -A 5 -B 5 'FindMyMac'"
            output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)
            if "FindMyMac = 1" in output:
                return "FindMy is ENABLED on this device."
            elif "FindMyMac = 0" in output:
                return "FindMy is DISABLED on this device."
            else:
                # If the first check doesn't give clear results, try an alternate method
                command2 = "defaults read /Library/Preferences/com.apple.FindMyMac.plist FMMEnabled"
                output2 = subprocess.check_output(command2, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)
                if "1" in output2:
                    return "FindMy is ENABLED on this device (alternate check)."
                else:
                    return "FindMy is DISABLED on this device (alternate check)."
        except subprocess.CalledProcessError:
            # If both methods fail, try a third method
            try:
                command3 = "sudo profiles show | grep -i 'find my'"
                output3 = self.run_sudo_command(command3)
                if "Find My Mac" in output3:
                    return "FindMy appears to be configured on this device."
                else:
                    return "FindMy status could not be determined with certainty. It may be DISABLED."
            except:
                return "Failed to check FindMy status."
        except subprocess.TimeoutExpired:
            return "FindMy status check timed out."
    def check_bluetooth_devices(self):
        """Check for paired Bluetooth devices"""
        try:
            # Use system_profiler to get Bluetooth information
            command = "system_profiler SPBluetoothDataType"
            output = subprocess.check_output(command, shell=True, text=True, stderr=subprocess.STDOUT, timeout=10)
            # Parse the output to find paired devices
            lines = output.split('\n')
            devices = []
            current_device = None
            for line in lines:
                if "Paired:" in line and "Yes" in line:
                    if current_device:
                        devices.append(current_device)
                elif "Connected:" in line and current_device:
                    status = "Connected" if "Yes" in line else "Not Connected"
                    devices.append(f"{current_device} - {status}")
                    current_device = None
                elif ":" in line and not line.strip().startswith("Paired") and not line.strip().startswith("Connected"):
                    current_device = line.strip().rstrip(':')
            if devices:
                return f"Found {len(devices)} paired Bluetooth devices:\n" + "\n".join(devices)
            else:
                return "No paired Bluetooth devices found."
        except subprocess.CalledProcessError:
            return "Failed to check Bluetooth devices."
        except subprocess.TimeoutExpired:
            return "Bluetooth devices check timed out."
    def check_profiles_installed(self):
        """Check for installed profiles"""
        try:
            # Use profiles command to list all profiles
            command = "profiles show -all"
            output = self.run_sudo_command(command)
            if not output or "Error" in output:
                # Try alternate command if the first one fails
                command = "profiles list"
                output = self.run_sudo_command(command)
            if not output or "Error" in output:
                return "No profiles found or unable to retrieve profiles information."
            # Parse the output to count and list profiles
            lines = output.split('\n')
            profile_count = 0
            profiles = []
            for line in lines:
                if "_computerlevel" in line or "attribute:" in line:
                    profile_count += 1
                    profile_name = line.strip()
                    profiles.append(profile_name)
            if profile_count > 0:
                return f"Found {profile_count} installed profiles:\n" + "\n".join(profiles)
            else:
                return "No profiles installed on this device."
        except Exception as e:
            return f"Failed to check installed profiles: {str(e)}"
    def on_window_resize(self, event):
        """Handle window resize events"""
        if event.widget == self:
            # Ensure minimum window size
            if self.winfo_height() < 300:
                self.geometry(f"{self.winfo_width()}x300")
    
    def on_closing(self):
        """Handle window closing"""
        logging.info("Application closing")
        self.quit()
        self.destroy()

def main():
    print("=== main() called ===")
    app = InternetCheckerApp()
    app.mainloop()

print('=== VS Mac Security Checker: Script started ===')
try:
    if __name__ == "__main__":
        main()
except Exception as e:
    import traceback
    with open(os.path.expanduser("~/Library/Logs/vs_mac_security_checker/vs_mac_security_checker.log"), "a") as f:
        f.write("\nFATAL ERROR:\n" + traceback.format_exc() + "\n")
    print("FATAL ERROR:", e)
    print(traceback.format_exc())
